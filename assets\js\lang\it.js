// Italian language file
export default {
  dir: "ltr",
  settings: {
    title: "Impostazioni",
    save: "Salva",
    close: "<PERSON>udi",
    language: "Lingua",
  },
  info: {
    title: "Informazioni",
    pwaInstall: {
      title: "Installa come App",
      description:
        "Installa scTimer come Progressive Web App per la migliore esperienza. Funziona offline e si sente come un'app nativa.",
      install: "Installa App",
      iosTitle: "Installazione iOS/iPad:",
      iosStep1: "1. Tocca il pulsante Condividi",
      iosStep2:
        '2. Scorri verso il basso e tocca "Aggiungi alla schermata Home"',
      iosStep3: '3. Tocca "Aggiungi" per installare',
      note: "Disponibile su Chrome, Safari e altri browser moderni",
    },
    shortcuts: {
      title: "Scorciatoie da Tastiera",
      timer: "Controlli Timer",
      spacebar: "Avvia/ferma timer",
      escape: "Annulla ispezione e chiudi modali",
      navigation: "Navigazione e Azioni",
      generate: "Genera nuovo scramble",
      list: "Attiva/disattiva lista tempi",
      settings: "Apri impostazioni",
      edit: "Modifica scramble corrente",
      copy: "Copia scramble negli appunti",
      stats: "Apri statistiche dettagliate",
      display: "Attiva/Disattiva Display",
      visualization: "Attiva/disattiva visualizzazione puzzle",
      statistics: "Attiva/disattiva visualizzazione statistiche",
      darkMode: "Attiva/disattiva modalità scura",
      inspection: "Attiva/disattiva ispezione WCA",
      penalties: "Gestione Penalità",
      removePenalty: "Rimuovi penalità dall'ultima risoluzione",
      addPlus2: "Aggiungi penalità +2 all'ultima risoluzione",
      addDNF: "Aggiungi penalità DNF all'ultima risoluzione",
      session: "Gestione Sessioni",
      emptySession: "Svuota sessione corrente",
      exportSession: "Esporta sessione corrente",
      eventSwitching: "Cambio Eventi",
      alt2to7: "Passa ai cubi 2×2×2 fino a 7×7×7",
      altP: "Passa al Pyraminx",
      altM: "Passa al Megaminx",
      altC: "Passa al Clock",
      altS: "Passa allo Skewb",
      alt1: "Passa al Square-1",
      altF: "Passa al 3×3×3 Meno Mosse",
      altO: "Passa al 3×3×3 Una Mano",
      blindfolded: "Eventi Bendati",
      altCtrl3: "Passa al 3×3×3 Bendato",
      altCtrl4: "Passa al 4×4×4 Bendato",
      altCtrl5: "Passa al 5×5×5 Bendato",
      altCtrl6: "Passa al 3×3×3 Multi-Bendato",
      sessionMgmt: "Gestione Sessioni",
      altN: "Crea nuova sessione",
    },
    gestures: {
      title: "Gesti Mobili",
      swipeDown: "Scorri Giù",
      swipeDownDesc: "Elimina ultima risoluzione",
      swipeUp: "Scorri Su",
      swipeUpDesc: "Alterna penalità (nessuna/+2/DNF)",
      swipeLeft: "Scorri a Sinistra",
      swipeLeftDesc: "LTR: Nuovo scramble | RTL: Lista tempi",
      swipeRight: "Scorri a Destra",
      swipeRightDesc: "LTR: Lista tempi | RTL: Nuovo scramble",
      doubleClick: "Doppio Click",
      doubleClickDesc: "Copia scramble corrente (PC/Mobile)",
      longPress: "Pressione Lunga/Click e Tieni",
      longPressDesc: "Modifica scramble corrente (PC/Mobile)",
    },
    features: {
      title: "Caratteristiche Principali",
      timer: "Timer Professionale",
      timerDesc: "Cronometraggio conforme WCA con modalità ispezione",
      puzzles: "Tutti gli Eventi WCA",
      puzzlesDesc: "Supporto completo per tutti gli eventi ufficiali WCA",
      statistics: "Statistiche Avanzate",
      statisticsDesc: "Analisi dettagliate con ao5, ao12, ao100",
      scrambles: "Scramble Ufficiali",
      scramblesDesc: "Generazione scramble standard WCA con visualizzazione 2D",
      multilingual: "Supporto Multilingue",
      multilingualDesc: "15+ lingue con supporto RTL",
      sync: "Sincronizzazione Google Drive",
      syncDesc: "Sincronizzazione cross-device con fusione intelligente",
    },
    sync: {
      title: "Sincronizzazione Google Drive",
      description:
        "Sincronizza i tuoi tempi di risoluzione su tutti i dispositivi usando Google Drive. I tuoi dati sono memorizzati in sicurezza nel tuo account Google Drive personale.",
      secure: "Sicuro e Privato",
      automatic: "Sincronizzazione Automatica",
      offline: "Supporto Offline",
      smartMerge: "Fusione Intelligente",
      note: "Abilita la sincronizzazione Google Drive nelle Impostazioni per mantenere i tuoi tempi sincronizzati su tutti i tuoi dispositivi.",
      status: "Stato:",
      notConnected: "Non connesso",
      connected: "Connesso",
      connect: "Connetti",
      disconnect: "Disconnetti",
      upload: "Carica su Drive",
      download: "Scarica da Drive",
      autoSync: "Sincronizzazione Automatica",
      autoSyncNote:
        "Sincronizza automaticamente i tuoi tempi quando sei connesso a Internet",
      uploading: "Caricamento...",
      downloading: "Scaricamento...",
      syncing: "Sincronizzazione...",
      uploadSuccess: "Caricamento riuscito",
      downloadSuccess: "Scaricamento riuscito",
      uploadFailed: "Caricamento fallito",
      downloadFailed: "Scaricamento fallito",
      uploadConfirm: "Caricare e unire i tuoi tempi locali su Google Drive?",
      downloadConfirm:
        "Scaricare e unire i dati da Google Drive con i tuoi tempi locali?",
      downloadMergeConfirm:
        "Questo unirà i dati di Google Drive con i tuoi tempi locali. Continuare?",
      reloadConfirm: "Ricaricare la pagina per vedere le modifiche?",
      autoSyncEnabled: "Sincronizzazione automatica abilitata",
      signInFailed: "Accesso fallito",
      noSyncFile: "File di sincronizzazione non trovato",
      noDataFound: "Nessun dato trovato",
      uploadCancelled: "Caricamento annullato",
      downloadCancelled: "Scaricamento annullato",
      syncSuccessful: "Sincronizzazione riuscita",
      syncFailed: "Sincronizzazione fallita",
      error: "Errore",
    },
  },
  timerOptions: {
    title: "Opzioni Timer",
    warningSounds: "Abilita Suoni di Avviso",
    useInspection: "Usa Ispezione WCA (15s)",
    inspectionSound: "Suono Ispezione:",
    inspectionSoundNone: "Nessuno",
    inspectionSoundVoice: "Voce",
    inspectionSoundBeep: "Beep",
    stackmatResetInspection: "Reset Stackmat Attiva Ispezione",
    stackmatResetNote: "Nota: Funziona solo quando il timer non è a 0.000",
    inputTimer: "Modalità Timer di Input (Inserire tempi manualmente)",
    timerMode: "Modalità Timer:",
    timerModeTimer: "Timer",
    timerModeTyping: "Digitazione",
    timerModeStackmat: "Stackmat",
    timerModeBluetooth: "Bluetooth (Prossimamente)",
    microphoneInput: "Input Microfono",
    microphoneAuto: "Rilevamento automatico",
    microphoneNote: "Scegli il tuo splitter Y o microfono esterno",
    decimalPlaces: "Cifre Decimali:",
    decimalPlacesNone: "Nessuna (12)",
    decimalPlaces1: "1 (12.3)",
    decimalPlaces2: "2 (12.34)",
    decimalPlaces3: "3 (12.345)",
  },
  displayOptions: {
    title: "Opzioni Display",
    showVisualization: "Mostra Visualizzazione Puzzle",
    showStats: "Mostra Statistiche",
    showDebug: "Mostra Informazioni Debug",
    darkMode: "Modalità Scura",
    showFMCKeyboard: "Mostra Tastiera FMC",
    scrambleFontSize: "Dimensione Font Scramble",
  },
  app: {
    title: "scTimer",
    description: "Un timer per speedcubing con ispezione WCA e statistiche",
    enterTime: "Inserisci tempo",
    enterSolveTime: "Inserisci tempo risoluzione manualmente",
    generateScrambles: "Genera Scramble",
    outOf: "Su:",
    numberOfCubes: "Numero di cubi (minimo 2):",
    numberOfCubesSolved: "Numero di cubi risolti:",
  },
  timer: {
    ready: "Pronto",
    running: "In esecuzione",
    idle: "Inattivo",
    inspection: "Ispezione",
    holding: "Tenendo",
  },
  stats: {
    title: "Statistiche",
    best: "Migliore",
    worst: "Peggiore",
    mean: "Media",
    avg5: "ao5",
    avg12: "ao12",
    mean3: "mo3",
    bestMo3: "Migliore mo3",
    avg100: "ao100",
    avg1000: "ao1000",
    solves: "Risoluzioni",
    attempts: "Tentativi",
    moreStats: "Più Statistiche",
  },
  statsDetails: {
    title: "Dettagli Statistiche",
    titleFor: "Dettagli Statistiche per",
    overview: "Panoramica",
    averages: "Medie",
    records: "Record",
    timeDistribution: "Distribuzione Tempi",
    progressChart: "Grafico Progresso",
    sessionAnalysis: "Analisi Sessione",
    predictions: "Previsioni",
    standardDeviation: "Deviazione Standard",
    bestSingle: "Migliore Singolo",
    bestAo5: "Migliore ao5",
    bestAo12: "Migliore ao12",
    bestAo100: "Migliore ao100",
    bestAo1000: "Migliore ao1000",
    totalTime: "Tempo Totale",
    averageTime: "Tempo Medio",
    solvesPerHour: "Risoluzioni/Ora",
    consistency: "Consistenza",
    nextAo5: "Prossimo Obiettivo ao5",
    nextAo12: "Prossimo Obiettivo ao12",
    improvementRate: "Tasso di Miglioramento",
    targetTime: "Tempo Obiettivo",
    currentSession: "Sessione Corrente",
    allSessions: "Tutte le Sessioni",
    importTimes: "Importa Tempi",
    exportJSON: "Esporta JSON",
    exportCSV: "Esporta CSV",
  },
  solveDetails: {
    title: "Dettagli Risoluzione",
    time: "Tempo",
    date: "Data",
    scramble: "Scramble",
    editedScramble: "Scramble Modificato",
    copyScramble: "Copia scramble",
    penalty: "Penalità",
    none: "Nessuna",
    comment: "Commento",
    addComment: "Aggiungi un commento...",
    save: "Salva",
    share: "Condividi",
    plusTwo: "+2",
    dnf: "DNF",
  },
  gestures: {
    scrambleCopied: "Scramble copiato",
    noSolvesToDelete: "Nessuna risoluzione da eliminare",
    solveDeleted: "Risoluzione eliminata",
    cannotAddPenaltyMBLD:
      "Impossibile aggiungere penalità alla risoluzione MBLD",
    dnfRemoved: "DNF rimosso",
    dnfAdded: "DNF aggiunto",
    plus2Added: "Penalità +2 aggiunta",
    penaltyRemoved: "Penalità rimossa",
    newScrambleGenerated: "Nuovo scramble generato",
    timesPanelOpened: "Pannello tempi aperto",
  },
  times: {
    title: "Tempi di Risoluzione",
    clear: "Cancella Tempi",
    close: "Chiudi",
    delete: "Elimina tempo",
    confirmClear:
      "Sei sicuro di voler cancellare tutti i tempi per questo evento?",
    confirmDelete: "Sei sicuro di voler eliminare questo tempo?",
  },
  buttons: {
    viewTimes: "Vedi Tempi",
    ok: "OK",
    cancel: "Annulla",
  },
  events: {
    333: "3×3×3",
    222: "2×2×2",
    444: "4×4×4",
    555: "5×5×5",
    666: "6×6×6",
    777: "7×7×7",
    "333bf": "3×3×3 Bendato",
    "333fm": "3×3×3 Meno Mosse",
    "333oh": "3×3×3 Una Mano",
    clock: "Clock",
    minx: "Megaminx",
    pyram: "Pyraminx",
    skewb: "Skewb",
    sq1: "Square-1",
    "444bf": "4×4×4 Bendato",
    "555bf": "5×5×5 Bendato",
    "333mbf": "3×3×3 Multi-Bendato",
  },
  mbld: {
    cubeCount: "Cubi",
    solvedCount: "Cubi Risolti",
    totalCount: "Cubi Totali",
    totalCubes: "Cubi Totali",
    cubesSolved: "Cubi Risolti",
    bestPoints: "Migliori Punti",
    successRate: "Tasso di Successo",
    points: "punti",
    save: "Salva Risultato",
    visualizations: "Visualizzazioni Multi-Bendato",
    scrambles: "Scramble Multi-Bendato",
    enterValidNumber: "Per favore inserisci un numero valido di cubi risolti.",
    noScrambles:
      "Nessuno scramble MBLD disponibile. Per favore seleziona prima l'evento 3×3×3 Multi-Bendato.",
    visualizationNotFound:
      "Modale visualizzazione non trovato. Per favore ricarica la pagina e riprova.",
    containerNotFound:
      "Container visualizzazione non trovato. Per favore ricarica la pagina e riprova.",
    clickToView:
      "Clicca per vedere tutte le visualizzazioni e scramble dei cubi",
    bestScore: "Miglior Punteggio",
    worstScore: "Peggior Punteggio",
    meanScore: "Punteggio Medio",
    averageScore: "Punteggio Medio",
    attempts: "tentativi",
    totalAttempts: "Tentativi Totali",
    clickToViewScrambles: "Clicca per vedere tutti gli scramble",
    clickToViewScramblesCount: "Clicca per vedere tutti i {0} scramble",
    setup: "Configurazione Multi-Bendato",
    results: "Risultati Multi-Bendato",
    generateScrambles: "Genera Scramble",
    saveResult: "Salva Risultato",
    cubeNumber: "Cubo",
    numberOfCubesMinimum: "Numero di cubi (minimo 2):",
    numberOfCubesSolved: "Numero di cubi risolti:",
    saveFirst: "Per favore salva prima il tuo risultato.",
    visualizationsTitle: "Visualizzazioni Multi-Bendato ({0} cubi)",
    timeLimit: "Limite di tempo: {0} minuti",
    timeLimitExceeded: "Limite di tempo superato. Il risultato sarà DNF.",
    negativePoints: "Punti negativi. Il risultato sarà DNF.",
  },
  modals: {
    error: "Errore",
    warning: "Avviso",
    info: "Informazione",
    confirm: "Conferma",
    prompt: "Input Richiesto",
  },
  stackmat: {
    error: "Errore Stackmat",
    noMicrophone:
      "Avvio timer Stackmat fallito: Nessun microfono trovato. Per favore collega un microfono e riprova.",
    connected: "Connesso",
    disconnected: "Disconnesso",
    settingUp: "Configurazione...",
  },
  sessions: {
    newSessionTitle: "Nuova Sessione",
    editSessionTitle: "Modifica Sessione",
    sessionName: "Nome Sessione:",
    sessionNamePlaceholder: "La Mia Sessione",
    puzzleType: "Tipo Puzzle:",
    create: "Crea",
    save: "Salva",
  },
  scramble: {
    loading: "Caricamento scramble...",
  },
  debug: {
    timerState: "Stato Timer: ",
    spaceHeldFor: "Spazio Tenuto Per: ",
    currentEvent: "Evento Corrente: ",
    scrambleSource: "Fonte Scramble: ",
  },
  fmc: {
    title: "Sfida Meno Mosse",
    info: "Risolvi il cubo con il minor numero di mosse possibile. Hai 60 minuti per trovare una soluzione.",
    timeRemaining: "Tempo Rimanente:",
    scramble: "Scramble:",
    solution: "Soluzione:",
    moveCount: "Mosse:",
    moves: "mosse",
    submit: "Invia",
    resultTitle: "Risultato FMC",
    resultTime: "Tempo:",
    resultSolution: "Soluzione:",
    resultOk: "OK",
    solutionPlaceholder:
      "Inserisci la tua soluzione qui usando la notazione standard WCA...",
    notationHelp: "Aiuto Notazione:",
    notationHelpContent:
      "Giri facce: U, D, L, R, F, B (con suffissi ' o 2)<br>Mosse larghe: Uw, Dw, ecc.<br>Mosse slice: M, E, S<br>Rotazioni: x, y, z (non contate nel totale mosse)",
    submitSolution: "Invia Soluzione",
    validSolution: "Soluzione valida",
    invalidNotation: "Notazione non valida rilevata",
    bestMoves: "Migliori Mosse",
    worstMoves: "Peggiori Mosse",
    meanMoves: "Mosse Medie",
    bestMo3: "Migliore mo3",
    averageMoves: "Mosse Medie",
    attempts: "tentativi",
    totalAttempts: "Tentativi Totali",
    tooManyMoves: "La soluzione supera il limite di 80 mosse",
    timeExceeded:
      "Limite di tempo superato. La tua soluzione sarà contrassegnata come DNF se non inviata.",
    confirmClose:
      "Sei sicuro di voler chiudere? Il tuo tentativo sarà contrassegnato come DNF.",
    dnfReasonTimeout: "Limite di tempo superato",
    dnfReasonInvalid: "Notazione non valida",
    dnfReasonTooManyMoves: "La soluzione supera 80 mosse",
    dnfReasonAbandoned: "Tentativo abbandonato",
    confirmSubmit: "Sei sicuro di voler inviare la tua soluzione?",
    pressToStart: "Premi spazio per iniziare il tentativo FMC",
    solutionAccepted: "Soluzione accettata",
    clickToViewTwizzle:
      "Clicca sul link sotto per vedere la soluzione in Twizzle",
    viewOnTwizzle: "Vedi su Twizzle",
    moveCountLabel: "Numero di mosse:",
    movesHTM: "mosse (HTM)",
    timeUsedLabel: "Tempo utilizzato:",
    loadingFMC: "caricamento FMC",
    generatingScramble: "generazione scramble e preparazione interfaccia",
  },
  tutorial: {
    welcomeTitle: "Benvenuto in scTimer!",
    welcomeSubtitle: "Il tuo timer professionale per speedcubing",
    selectLanguage: "Seleziona Lingua:",
    feature1: "Timer Standard WCA",
    feature2: "Statistiche Avanzate",
    feature3: "Tutti gli Eventi WCA",
    feature4: "Generatore Scramble",
    welcomeDescription:
      "Vorresti un tour veloce per imparare come usare scTimer efficacemente? Il tutorial ti guiderà attraverso le funzionalità principali in pochi passi.",
    skipTutorial: "Salta Tutorial",
    startTour: "Inizia Tour",
    step1: {
      title: "Display Scramble",
      text: "Questo mostra la sequenza scramble per il tuo puzzle corrente. Ogni scramble è generato casualmente seguendo gli standard WCA.",
    },
    step2: {
      title: "Controlli Timer",
      text: "Premi e tieni premuto SPAZIO per iniziare il cronometraggio, rilascia per iniziare a risolvere. Su mobile, tocca e tieni premuta l'area del timer. Il timer segue gli standard di ispezione WCA.",
    },
    step3: {
      title: "Selettore Eventi",
      text: "Scegli tra tutti gli eventi WCA inclusi 3x3x3, 2x2x2, 4x4x4 e molti altri tipi di puzzle. Clicca o tocca per aprire il menu a discesa.",
    },
    step4: {
      title: "Tracciamento Statistiche",
      text: "Traccia i tuoi progressi con statistiche dettagliate inclusi miglior tempo, medie di 5, 12 e 100 risoluzioni. Clicca su qualsiasi statistica per vedere più dettagli.",
    },
    step5: {
      title: "Genera Nuovo Scramble",
      text: "Genera un nuovo scramble quando sei pronto per la tua prossima risoluzione. Scorciatoia da tastiera: Premi N o clicca sull'icona shuffle.",
    },
    step6: {
      title: "Impostazioni e Personalizzazione",
      text: "Personalizza la tua esperienza timer con tempo di ispezione, opzioni audio, modalità timer e preferenze display. Scorciatoia da tastiera: Premi S.",
    },
    step7: {
      title: "Scorciatoie da Tastiera",
      text: "Padroneggia queste scorciatoie: SPAZIO (avvia/ferma timer), N (nuovo scramble), S (impostazioni), ESC (chiudi modali), Frecce (naviga). Su mobile, usa i gesti di scorrimento!",
    },
    step8: {
      title: "Gesti Mobili",
      text: "Su dispositivi mobili: Scorri a sinistra per aprire il pannello tempi, scorri a destra per chiuderlo, tocca e tieni il timer per avviare, doppio tocco sullo scramble per copiare. Pizzica per zoomare sulle visualizzazioni.",
    },
    step9: {
      title: "Consigli Pro e Funzionalità",
      text: "Abilita il tempo di ispezione nelle impostazioni per la pratica WCA. Usa sessioni diverse per tracciare vari eventi. Esporta i tuoi tempi per l'analisi. Il timer funziona offline come PWA!",
    },
    previous: "Precedente",
    next: "Successivo",
    finish: "Fine",
    close: "Chiudi",
    stepCounter: "di",
    restartTutorial: "Riavvia Tutorial",
  },
};

// Stackmat Timer Manager
// Handles integration with Stackmat timer hardware via audio input

class StackmatManager {
  constructor() {
    this.stackmat = null;
    this.isConnected = false;
    this.isCapturing = false;
    this.currentDisplayTime = 0; // The time currently being displayed
    this.lastRunningTime = 0; // Last time captured while status was RUNNING
    this.timerState = "idle"; // idle, ready, running, stopped
    this.lastPacketStatus = "I"; // Track previous packet status to detect transitions
    this.lastLeftHandState = false; // Track left hand state for tap detection
    this.leftHandDownTime = 0; // When left hand was pressed down
    this.lastBothHandsState = false; // Track both hands state for ready detection
    this.potentialTapStartTime = null; // Track potential tap timing
    this.quickTapStartTime = null; // Track quick tap timing
    this.hasSeenNonZeroTime = false; // Track if we've seen a non-zero time (to avoid initial 0.000 trigger)
    this.onTimeUpdate = null;
    this.onStateChange = null;
    this.onTimerStart = null;
    this.onTimerStop = null;
    this.onLeftHandTap = null; // Callback for left hand tap
    this.onBothHandsChange = null; // Callback for both hands state change
    this.connectionCheckInterval = null;
    this.lastPacketTime = 0;
    this.connectionTimeout = 3000; // 3 seconds without packets = disconnected
  }

  // Initialize Stackmat timer
  async init() {
    try {
      // Check if Stackmat is available globally
      if (typeof Stackmat === "undefined") {
        throw new Error("Stackmat library not loaded");
      }

      // Suppress console logs during Stackmat initialization
      const originalConsoleLog = console.log;
      console.log = () => {};
      try {
        this.stackmat = new Stackmat();
        this.setupEventHandlers();
      } finally {
        console.log = originalConsoleLog;
      }

      return true;
    } catch (error) {
      console.error("Failed to initialize Stackmat:", error);
      return false;
    }
  }

  // Setup event handlers for Stackmat events
  setupEventHandlers() {
    if (!this.stackmat) return;

    // Packet received - update time display
    this.stackmat.on("packetReceived", (packet) => {
      this.lastPacketTime = Date.now();

      // Store previous time BEFORE updating current time
      const previousDisplayTime = this.currentDisplayTime;

      // Always track the current display time - this is what the user sees
      this.currentDisplayTime = packet.timeInMilliseconds;

      // Specifically capture time when status is RUNNING (space character)
      if (packet.status === " " && packet.timeInMilliseconds > 0) {
        this.lastRunningTime = packet.timeInMilliseconds;
      }

      // Detect left hand tap for inspection (pass previous time)
      this.detectLeftHandTap(packet, previousDisplayTime);

      // Detect both hands state for ready indication
      this.detectBothHandsState(packet);

      // Detect transition from RUNNING to IDLE (timer stopped)
      if (
        this.lastPacketStatus === " " &&
        packet.status === "I" &&
        this.lastRunningTime > 0
      ) {
        this.handleTimerStopped();
      }

      // Update last packet status for next comparison
      this.lastPacketStatus = packet.status;

      if (this.onTimeUpdate) {
        this.onTimeUpdate(packet.timeInMilliseconds, packet.timeAsString);
      }
    });

    // Timer connected
    this.stackmat.on("timerConnected", () => {
      this.isConnected = true;
      if (this.onStateChange) {
        this.onStateChange("connected");
      }
    });

    // Timer disconnected
    this.stackmat.on("timerDisconnected", () => {
      this.isConnected = false;
      this.timerState = "idle";
      if (this.onStateChange) {
        this.onStateChange("disconnected");
      }
    });

    // Timer ready (both hands on timer)
    this.stackmat.on("ready", () => {
      this.timerState = "ready";
      if (this.onStateChange) {
        this.onStateChange("ready");
      }
    });

    // Timer unready (hands lifted before start)
    this.stackmat.on("unready", () => {
      this.timerState = "idle";
      if (this.onStateChange) {
        this.onStateChange("unready");
      }
    });

    // Timer starting (hands lifted to start)
    this.stackmat.on("starting", () => {
      this.timerState = "starting";
      if (this.onStateChange) {
        this.onStateChange("starting");
      }
    });

    // Timer started (timing began)
    this.stackmat.on("started", () => {
      this.timerState = "running";
      this.lastRunningTime = 0; // Reset for new solve
      if (this.onTimerStart) {
        this.onTimerStart();
      }
      if (this.onStateChange) {
        this.onStateChange("started");
      }
    });

    // Timer stopped (hands placed back on timer)
    this.stackmat.on("stopped", (packet) => {
      this.timerState = "stopped";

      // Use the last running time - this is the most accurate
      // The stopped packet often has timeInMilliseconds = 0
      const timeToSave =
        this.lastRunningTime > 0
          ? this.lastRunningTime
          : this.currentDisplayTime;

      if (this.onTimerStop) {
        this.onTimerStop(timeToSave);
      }
      if (this.onStateChange) {
        this.onStateChange("stopped", timeToSave);
      }
    });

    // Timer reset
    this.stackmat.on("reset", () => {
      this.timerState = "idle";
      this.currentTime = 0;
      if (this.onStateChange) {
        this.onStateChange("reset");
      }
    });
  }

  // Detect if running on mobile device
  isMobileDevice() {
    return /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
      navigator.userAgent
    );
  }

  // Check available audio input devices
  async getAudioInputDevices() {
    try {
      if (!navigator.mediaDevices || !navigator.mediaDevices.enumerateDevices) {
        return [];
      }

      const devices = await navigator.mediaDevices.enumerateDevices();
      return devices.filter((device) => device.kind === "audioinput");
    } catch (error) {
      console.warn("Could not enumerate audio devices:", error);
      return [];
    }
  }

  // Test audio input level (useful for diagnosing Y splitter issues)
  async testAudioInput(duration = 3000) {
    return new Promise((resolve, reject) => {
      const isMobile = this.isMobileDevice();

      const constraints = {
        audio: {
          echoCancellation: false,
          noiseSuppression: false,
          autoGainControl: false,
          latency: isMobile ? 0.005 : 0.01,
          channelCount: 1,
        },
      };

      navigator.mediaDevices
        .getUserMedia(constraints)
        .then((stream) => {
          const audioContext = new AudioContext();
          const source = audioContext.createMediaStreamSource(stream);
          const analyser = audioContext.createAnalyser();

          analyser.fftSize = 256;
          const bufferLength = analyser.frequencyBinCount;
          const dataArray = new Uint8Array(bufferLength);

          source.connect(analyser);

          let maxLevel = 0;
          let avgLevel = 0;
          let samples = 0;

          const checkLevel = () => {
            analyser.getByteFrequencyData(dataArray);

            let sum = 0;
            let max = 0;
            for (let i = 0; i < bufferLength; i++) {
              sum += dataArray[i];
              if (dataArray[i] > max) max = dataArray[i];
            }

            const currentAvg = sum / bufferLength;
            avgLevel = (avgLevel * samples + currentAvg) / (samples + 1);
            samples++;

            if (max > maxLevel) maxLevel = max;
          };

          const interval = setInterval(checkLevel, 100);

          setTimeout(() => {
            clearInterval(interval);

            // Clean up
            stream.getTracks().forEach((track) => track.stop());
            audioContext.close();

            resolve({
              maxLevel,
              avgLevel,
              hasSignal: maxLevel > 10, // Threshold for detecting audio signal
              isLikelyStackmat: maxLevel > 50 && avgLevel > 5, // Rough heuristic for Stackmat signal
            });
          }, duration);
        })
        .catch(reject);
    });
  }

  // Auto-detect Stackmat signal across all available microphones
  async autoDetectStackmatMicrophone() {
    const audioDevices = await this.getAudioInputDevices();

    for (let i = 0; i < audioDevices.length; i++) {
      const device = audioDevices[i];

      try {
        const hasStackmatSignal = await this.testDeviceForStackmat(
          device.deviceId
        );
        if (hasStackmatSignal) {
          return device.deviceId;
        }
      } catch (error) {
        // Continue to next device
      }
    }

    return null;
  }

  // Test a specific device for Stackmat signal
  async testDeviceForStackmat(deviceId, testDuration = 4000) {
    return new Promise((resolve, reject) => {
      const constraints = {
        audio: {
          deviceId: deviceId ? { exact: deviceId } : undefined,
          echoCancellation: false,
          noiseSuppression: false,
          autoGainControl: false,
          channelCount: 1,
          sampleRate: 48000,
        },
      };

      navigator.mediaDevices
        .getUserMedia(constraints)
        .then((stream) => {
          const audioContext = new AudioContext();
          const source = audioContext.createMediaStreamSource(stream);
          const analyser = audioContext.createAnalyser();

          analyser.fftSize = 2048; // Higher resolution for frequency analysis
          analyser.smoothingTimeConstant = 0.1;
          const bufferLength = analyser.frequencyBinCount;
          const frequencyData = new Uint8Array(bufferLength);
          source.connect(analyser);

          let stackmatSignalCount = 0;
          let totalSamples = 0;
          let maxSignalLevel = 0;
          let targetFrequencyHits = 0;
          const startTime = Date.now();

          // Stackmat timer characteristics:
          // - Frequency around 1200Hz (carrier frequency)
          // - Consistent signal pattern with data packets
          // - Specific amplitude characteristics
          // - Regular timing intervals

          const checkSignal = () => {
            analyser.getByteFrequencyData(frequencyData);

            // Calculate frequency bin for ~1200Hz (Stackmat carrier frequency)
            const sampleRate = audioContext.sampleRate;
            const targetFreq = 1200; // Stackmat carrier frequency
            const binSize = sampleRate / analyser.fftSize;
            const targetBin = Math.round(targetFreq / binSize);

            // Check for signal around 1200Hz with some tolerance
            const targetSignal = frequencyData[targetBin] || 0;
            const nearbySignals = [
              frequencyData[targetBin - 3] || 0,
              frequencyData[targetBin - 2] || 0,
              frequencyData[targetBin - 1] || 0,
              frequencyData[targetBin] || 0,
              frequencyData[targetBin + 1] || 0,
              frequencyData[targetBin + 2] || 0,
              frequencyData[targetBin + 3] || 0,
            ];

            const avgNearbySignal =
              nearbySignals.reduce((a, b) => a + b, 0) / nearbySignals.length;
            const maxNearbySignal = Math.max(...nearbySignals);

            // Calculate overall signal level
            let currentMaxSignal = 0;
            for (let i = 0; i < frequencyData.length; i++) {
              if (frequencyData[i] > currentMaxSignal) {
                currentMaxSignal = frequencyData[i];
              }
            }

            if (currentMaxSignal > maxSignalLevel) {
              maxSignalLevel = currentMaxSignal;
            }

            // Calculate noise floor (average of low frequencies)
            const noiseFloor =
              frequencyData.slice(0, 50).reduce((a, b) => a + b, 0) / 50;

            // Check for Stackmat-like characteristics:
            // 1. Strong signal around 1200Hz
            // 2. Signal significantly above noise floor
            // 3. Consistent frequency presence
            const hasTargetFrequency = maxNearbySignal > 100; // Strong 1200Hz signal
            const isAboveNoiseFloor = avgNearbySignal > noiseFloor * 3; // 3x above noise
            const signalToNoise = avgNearbySignal / (noiseFloor + 1);

            if (hasTargetFrequency) {
              targetFrequencyHits++;
            }

            // Very strict criteria for Stackmat detection
            if (hasTargetFrequency && isAboveNoiseFloor && signalToNoise > 4) {
              stackmatSignalCount++;
            }

            totalSamples++;

            if (Date.now() - startTime < testDuration) {
              requestAnimationFrame(checkSignal);
            } else {
              // Clean up
              stream.getTracks().forEach((track) => track.stop());
              audioContext.close();

              // Balanced criteria for Stackmat detection:
              const stackmatRatio = stackmatSignalCount / totalSamples;
              const targetFreqRatio = targetFrequencyHits / totalSamples;

              // Stackmat detection criteria (more practical):
              // 1. Strong signal around 1200Hz for at least 50% of samples
              // 2. High maximum signal level (>200) indicating strong audio
              // 3. Consistent frequency presence
              const hasStrongTargetFreq = targetFreqRatio > 0.5; // 50% of samples have 1200Hz
              const hasHighSignalLevel = maxSignalLevel > 200; // Strong signal strength
              const hasMinimumHits = targetFrequencyHits > 15; // Minimum frequency detections

              // Alternative detection: if we have very strong 1200Hz signal consistently
              const hasVeryStrongFreq =
                targetFreqRatio > 0.8 && maxSignalLevel > 180;

              const isStackmatLikely =
                (hasStrongTargetFreq && hasHighSignalLevel && hasMinimumHits) ||
                hasVeryStrongFreq;

              // Device test completed
              resolve(isStackmatLikely);
            }
          };

          checkSignal();
        })
        .catch(reject);
    });
  }

  // Global variable to track active audio streams
  static activeStreams = [];

  // Stop all active audio streams to ensure exclusive access
  static async stopAllActiveStreams() {
    // Stop our tracked streams
    StackmatManager.activeStreams.forEach((stream) => {
      if (stream && stream.getTracks) {
        stream.getTracks().forEach((track) => {
          if (track.readyState === "live") {
            track.stop();
          }
        });
      }
    });
    StackmatManager.activeStreams = [];

    // Try to stop any other potential audio streams
    try {
      // Request and immediately stop a stream to force release of audio resources
      const tempStream = await navigator.mediaDevices.getUserMedia({
        audio: {
          echoCancellation: false,
          noiseSuppression: false,
          autoGainControl: false,
        },
      });
      tempStream.getTracks().forEach((track) => track.stop());
    } catch (error) {
      // Ignore errors - this is just a cleanup attempt
    }

    // Small delay to ensure audio system releases resources
    await new Promise((resolve) => setTimeout(resolve, 200));
  }

  // Start capturing from Stackmat timer
  async startCapture(deviceId = null) {
    if (!this.stackmat) {
      throw new Error("Stackmat not initialized");
    }

    try {
      // Check if microphone is available
      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        throw new Error("Microphone access not supported in this browser");
      }

      // CRITICAL: Stop all existing audio streams first
      await StackmatManager.stopAllActiveStreams();

      const isMobile = this.isMobileDevice();
      let selectedDeviceId = deviceId;

      // Auto-detect Stackmat only if no specific device provided
      if (!selectedDeviceId) {
        selectedDeviceId = await this.autoDetectStackmatMicrophone();
      }

      // Enhanced audio constraints for exclusive access
      const audioConstraints = {
        echoCancellation: false,
        noiseSuppression: false,
        autoGainControl: false,
        latency: isMobile ? 0.005 : 0.01,
        channelCount: 1,
      };

      // Use detected/selected microphone device
      if (selectedDeviceId && selectedDeviceId !== "default") {
        audioConstraints.deviceId = { exact: selectedDeviceId };
      } else if (isMobile) {
        // On Android, try to use the best available input device
        audioConstraints.deviceId = { ideal: "default" };
      }

      // Request exclusive microphone access
      const exclusiveStream = await navigator.mediaDevices.getUserMedia({
        audio: audioConstraints,
      });

      // Track this stream for future cleanup
      StackmatManager.activeStreams.push(exclusiveStream);

      // Start Stackmat with the selected device ID (suppress console logs)
      const originalConsoleLog = console.log;
      console.log = () => {};
      try {
        this.stackmat.start(selectedDeviceId);
      } finally {
        console.log = originalConsoleLog;
      }

      this.isCapturing = true;
      this.startConnectionCheck();
      return true;
    } catch (error) {
      console.error("Failed to start Stackmat capture:", error);

      // Provide user-friendly error messages with mobile-specific guidance
      let userMessage = "Failed to start Stackmat timer: ";

      if (error.name === "NotFoundError") {
        if (this.isMobileDevice()) {
          userMessage +=
            "No microphone found. If using a Y splitter, make sure it's properly connected to the microphone port and try again.";
        } else {
          userMessage +=
            "No microphone found. Please connect a microphone and try again.";
        }
      } else if (error.name === "NotAllowedError") {
        userMessage +=
          "Microphone permission denied. Please allow microphone access and try again.";
      } else if (error.name === "NotSupportedError") {
        userMessage += "Microphone not supported in this browser.";
      } else if (error.name === "NotReadableError") {
        if (this.isMobileDevice()) {
          userMessage +=
            "Microphone is being used by another application, or there's an issue with the audio input device. Try closing other apps and reconnecting your Y splitter.";
        } else {
          userMessage += "Microphone is being used by another application.";
        }
      } else if (error.name === "OverconstrainedError") {
        userMessage +=
          "Audio constraints not supported by your device. This may happen with some mobile devices or external audio adapters.";
      } else {
        userMessage += error.message || "Unknown error occurred.";
      }

      // Add mobile-specific troubleshooting tips
      if (this.isMobileDevice()) {
        userMessage +=
          "\n\nMobile troubleshooting tips:\n" +
          "• Ensure your Y splitter is TRRS (4-pole) compatible\n" +
          "• Try reconnecting the Y splitter\n" +
          "• Check if other audio apps can access the microphone\n" +
          "• Try restarting your browser";
      }

      // Create a custom error with user-friendly message
      const userError = new Error(userMessage);
      userError.originalError = error;
      throw userError;
    }
  }

  // Stop capturing from Stackmat timer
  stopCapture() {
    if (this.stackmat && this.isCapturing) {
      this.stackmat.stop();
      this.isCapturing = false;
      this.isConnected = false;
      this.timerState = "idle";
      this.stopConnectionCheck();

      // Clean up our tracked streams
      StackmatManager.activeStreams.forEach((stream) => {
        if (stream && stream.getTracks) {
          stream.getTracks().forEach((track) => {
            if (track.readyState === "live") {
              track.stop();
            }
          });
        }
      });
      StackmatManager.activeStreams = [];
    }
  }

  // Diagnostic function for troubleshooting Android/mobile issues
  async runDiagnostics() {
    console.log("=== Stackmat Mobile Diagnostics ===");

    const isMobile = this.isMobileDevice();
    console.log("Mobile device detected:", isMobile);
    console.log("User agent:", navigator.userAgent);

    // Check browser capabilities
    console.log(
      "getUserMedia supported:",
      !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia)
    );
    console.log(
      "AudioContext supported:",
      !!(window.AudioContext || window.webkitAudioContext)
    );
    console.log("AudioWorklet supported:", !!window.AudioWorkletNode);

    // Check available audio devices
    try {
      const devices = await this.getAudioInputDevices();
      console.log("Audio input devices found:", devices.length);
      devices.forEach((device, index) => {
        console.log(
          `  Device ${index}: ${
            device.label || "Unknown"
          } (${device.deviceId.substring(0, 20)}...)`
        );
      });
    } catch (error) {
      console.error("Error enumerating devices:", error);
    }

    // Test basic audio access
    console.log("\nTesting basic audio access...");
    try {
      const stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          echoCancellation: false,
          noiseSuppression: false,
          autoGainControl: false,
        },
      });

      console.log("✓ Basic audio access successful");

      // Get audio track settings
      const audioTrack = stream.getAudioTracks()[0];
      if (audioTrack) {
        const settings = audioTrack.getSettings();
        console.log("Audio track settings:", {
          sampleRate: settings.sampleRate,
          channelCount: settings.channelCount,
          echoCancellation: settings.echoCancellation,
          noiseSuppression: settings.noiseSuppression,
          autoGainControl: settings.autoGainControl,
          deviceId: settings.deviceId?.substring(0, 20) + "...",
        });
      }

      stream.getTracks().forEach((track) => track.stop());

      // Test audio input levels
      console.log("\nTesting audio input levels (3 seconds)...");
      console.log("Please make some noise or connect your Stackmat timer...");

      const audioTest = await this.testAudioInput(3000);
      console.log("Audio test results:", audioTest);

      if (!audioTest.hasSignal) {
        console.warn(
          "⚠️  No audio signal detected. Check your microphone/Y splitter connection."
        );
      } else if (audioTest.isLikelyStackmat) {
        console.log("✓ Strong audio signal detected - likely Stackmat timer!");
      } else {
        console.log("✓ Audio signal detected, but may not be Stackmat timer.");
      }
    } catch (error) {
      console.error("✗ Audio access failed:", error);

      if (error.name === "NotAllowedError") {
        console.log(
          "💡 Try: Grant microphone permission and run diagnostics again"
        );
      } else if (error.name === "NotFoundError") {
        console.log("💡 Try: Connect your Y splitter to the microphone port");
      } else if (error.name === "NotReadableError") {
        console.log(
          "💡 Try: Close other apps using the microphone, reconnect Y splitter"
        );
      }
    }

    console.log("\n=== Diagnostics Complete ===");
    console.log("If issues persist:");
    console.log("1. Ensure Y splitter is TRRS (4-pole) compatible");
    console.log("2. Try different Y splitter or audio adapter");
    console.log("3. Test with csTimer to compare behavior");
    console.log("4. Check Android audio settings/permissions");
  }

  // Start connection monitoring
  startConnectionCheck() {
    this.lastPacketTime = Date.now();
    this.connectionCheckInterval = setInterval(() => {
      const timeSinceLastPacket = Date.now() - this.lastPacketTime;

      if (timeSinceLastPacket > this.connectionTimeout && this.isConnected) {
        // Simulate disconnection if no packets received
        this.isConnected = false;
        this.timerState = "idle";
        if (this.onStateChange) {
          this.onStateChange("disconnected");
        }
      }
    }, 1000);
  }

  // Stop connection monitoring
  stopConnectionCheck() {
    if (this.connectionCheckInterval) {
      clearInterval(this.connectionCheckInterval);
      this.connectionCheckInterval = null;
    }
  }

  // Get current timer state
  getState() {
    return {
      isConnected: this.isConnected,
      isCapturing: this.isCapturing,
      timerState: this.timerState,
      currentTime: this.currentTime,
    };
  }

  // Set callback for time updates
  setTimeUpdateCallback(callback) {
    this.onTimeUpdate = callback;
  }

  // Set callback for state changes
  setStateChangeCallback(callback) {
    this.onStateChange = callback;
  }

  // Set callback for timer start
  setTimerStartCallback(callback) {
    this.onTimerStart = callback;
  }

  // Set callback for timer stop
  setTimerStopCallback(callback) {
    this.onTimerStop = callback;
  }

  setLeftHandTapCallback(callback) {
    this.onLeftHandTap = callback;
  }

  setBothHandsChangeCallback(callback) {
    this.onBothHandsChange = callback;
  }

  // Handle timer stopped (detected from status transition)
  handleTimerStopped() {
    this.timerState = "stopped";

    // Use the current display time - this includes the time up to when hands were placed back
    const timeToSave = this.currentDisplayTime;

    if (this.onTimerStop) {
      this.onTimerStop(timeToSave);
    }
    if (this.onStateChange) {
      this.onStateChange("stopped", timeToSave);
    }
  }

  // Detect time reset to 0.000 for inspection trigger
  // Only triggers after we've seen a non-zero time (avoids initial load trigger)
  detectLeftHandTap(packet, previousTime) {
    const currentTime = packet.timeInMilliseconds;

    // Track if we've ever seen a non-zero time
    if (currentTime > 0) {
      this.hasSeenNonZeroTime = true;
    }

    // Only detect reset to 0 if:
    // 1. Current time is 0
    // 2. Previous time was greater than 0
    // 3. Timer is in idle state
    // 4. We've previously seen a non-zero time (not initial load)
    if (
      currentTime === 0 &&
      previousTime > 0 &&
      this.timerState === "idle" &&
      this.hasSeenNonZeroTime
    ) {
      this.triggerInspection("Time reset to 0.000");
    }
  }

  // Helper method to trigger inspection
  triggerInspection(method) {
    if (this.onLeftHandTap && this.timerState === "idle") {
      this.onLeftHandTap();
    }
  }

  // Detect both hands state for ready indication
  detectBothHandsState(packet) {
    // Use status 'A' (STARTING) to detect both hands ready state
    const currentBothHandsState = packet.status === "A";

    // Detect both hands state change
    if (this.lastBothHandsState !== currentBothHandsState) {
      // Only trigger callback if we have one and timer is in appropriate state
      if (
        this.onBothHandsChange &&
        (this.timerState === "idle" || this.timerState === "inspection")
      ) {
        this.onBothHandsChange(currentBothHandsState);
      }
    }

    // Update last both hands state for next comparison
    this.lastBothHandsState = currentBothHandsState;
  }

  // Format time for debugging
  formatTime(timeMs) {
    if (!timeMs || timeMs === 0) return "0.000";

    const minutes = Math.floor(timeMs / 60000);
    const seconds = Math.floor((timeMs % 60000) / 1000);
    const milliseconds = timeMs % 1000;

    if (minutes > 0) {
      return `${minutes}:${seconds.toString().padStart(2, "0")}.${milliseconds
        .toString()
        .padStart(3, "0")}`;
    } else {
      return `${seconds}.${milliseconds.toString().padStart(3, "0")}`;
    }
  }

  // Cleanup
  destroy() {
    this.stopCapture();
    this.stopConnectionCheck();
    this.stackmat = null;
    this.onTimeUpdate = null;
    this.onStateChange = null;
    this.onTimerStart = null;
    this.onTimerStop = null;
  }
}

// Export for use in other modules
window.StackmatManager = StackmatManager;

// Global diagnostic function for easy console access
window.stackmatDiagnostics = async function () {
  console.log("Running Stackmat diagnostics...");
  console.log(
    "Note: This creates a temporary StackmatManager instance for testing."
  );

  const tempManager = new StackmatManager();
  await tempManager.init();
  await tempManager.runDiagnostics();
  tempManager.destroy();

  console.log("\nTo run diagnostics again, type: stackmatDiagnostics()");
};

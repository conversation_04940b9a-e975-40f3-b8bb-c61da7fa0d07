// Portuguese language file
export default {
  dir: "ltr",
  settings: {
    title: "Configurações",
    save: "Salvar",
    close: "<PERSON><PERSON><PERSON>",
    language: "Idioma",
  },
  info: {
    title: "Informações",
    pwaInstall: {
      title: "Instalar como App",
      description:
        "Instale o scTimer como um Progressive Web App para a melhor experiência. Funciona offline e parece um app nativo.",
      install: "Instalar App",
      iosTitle: "Instalação iOS/iPad:",
      iosStep1: "1. Toque no botão Compartilhar",
      iosStep2: '2. Role para baixo e toque em "Adicionar à Tela Inicial"',
      iosStep3: '3. Toque em "Adicionar" para instalar',
      note: "Disponível no Chrome, Safari e outros navegadores modernos",
    },
    shortcuts: {
      title: "Atalhos do Teclado",
      timer: "Controles do Timer",
      spacebar: "Iniciar/parar timer",
      escape: "Cancelar inspeção e fechar modais",
      navigation: "Navegação e Ações",
      generate: "Gerar novo embaralhamento",
      list: "Alternar lista de tempos",
      settings: "Abrir configurações",
      edit: "Editar embaralhamento atual",
      copy: "Copiar embaralhamento para área de transferência",
      stats: "Abrir estatísticas detalhadas",
      display: "Alternar Exibição",
      visualization: "Alternar visualização do puzzle",
      statistics: "Alternar exibição de estatísticas",
      darkMode: "Alternar modo escuro",
      inspection: "Alternar inspeção WCA",
      penalties: "Gerenciamento de Penalidades",
      removePenalty: "Remover penalidade da última resolução",
      addPlus2: "Adicionar penalidade +2 à última resolução",
      addDNF: "Adicionar penalidade DNF à última resolução",
      session: "Gerenciamento de Sessões",
      emptySession: "Esvaziar sessão atual",
      exportSession: "Exportar sessão atual",
      eventSwitching: "Troca de Eventos",
      alt2to7: "Trocar para cubos 2×2×2 a 7×7×7",
      altP: "Trocar para Pyraminx",
      altM: "Trocar para Megaminx",
      altC: "Trocar para Clock",
      altS: "Trocar para Skewb",
      alt1: "Trocar para Square-1",
      altF: "Trocar para 3×3×3 Menos Movimentos",
      altO: "Trocar para 3×3×3 Uma Mão",
      blindfolded: "Eventos Vendados",
      altCtrl3: "Trocar para 3×3×3 Vendado",
      altCtrl4: "Trocar para 4×4×4 Vendado",
      altCtrl5: "Trocar para 5×5×5 Vendado",
      altCtrl6: "Trocar para 3×3×3 Multi-Vendado",
      sessionMgmt: "Gerenciamento de Sessões",
      altN: "Criar nova sessão",
    },
    gestures: {
      title: "Gestos Móveis",
      swipeDown: "Deslizar para Baixo",
      swipeDownDesc: "Deletar última resolução",
      swipeUp: "Deslizar para Cima",
      swipeUpDesc: "Alternar penalidades (nenhuma/+2/DNF)",
      swipeLeft: "Deslizar para Esquerda",
      swipeLeftDesc: "LTR: Novo embaralhamento | RTL: Lista de tempos",
      swipeRight: "Deslizar para Direita",
      swipeRightDesc: "LTR: Lista de tempos | RTL: Novo embaralhamento",
      doubleClick: "Duplo Clique",
      doubleClickDesc: "Copiar embaralhamento atual (PC/Móvel)",
      longPress: "Pressão Longa/Clique e Segurar",
      longPressDesc: "Editar embaralhamento atual (PC/Móvel)",
    },
    features: {
      title: "Recursos Principais",
      timer: "Timer Profissional",
      timerDesc: "Cronometragem compatível com WCA com modo de inspeção",
      puzzles: "Todos os Eventos WCA",
      puzzlesDesc: "Suporte completo para todos os eventos oficiais WCA",
      statistics: "Estatísticas Avançadas",
      statisticsDesc: "Análise detalhada com ao5, ao12, ao100",
      scrambles: "Embaralhamentos Oficiais",
      scramblesDesc:
        "Geração de embaralhamentos padrão WCA com visualização 2D",
      multilingual: "Suporte Multilíngue",
      multilingualDesc: "15+ idiomas com suporte RTL",
      sync: "Sincronização Google Drive",
      syncDesc: "Sincronização entre dispositivos com mesclagem inteligente",
    },
    sync: {
      title: "Sincronização Google Drive",
      description:
        "Sincronize seus tempos de resolução em todos os dispositivos usando Google Drive. Seus dados são armazenados com segurança em sua conta pessoal do Google Drive.",
      secure: "Seguro e Privado",
      automatic: "Sincronização Automática",
      offline: "Suporte Offline",
      smartMerge: "Mesclagem Inteligente",
      note: "Ative a sincronização do Google Drive nas Configurações para manter seus tempos sincronizados em todos os seus dispositivos.",
      status: "Status:",
      notConnected: "Não conectado",
      connected: "Conectado",
      connect: "Conectar",
      disconnect: "Desconectar",
      upload: "Enviar para Drive",
      download: "Baixar do Drive",
      autoSync: "Sincronização Automática",
      autoSyncNote:
        "Sincronize automaticamente seus tempos quando conectado à internet",
      uploading: "Enviando...",
      downloading: "Baixando...",
      syncing: "Sincronizando...",
      uploadSuccess: "Envio bem-sucedido",
      downloadSuccess: "Download bem-sucedido",
      uploadFailed: "Falha no envio",
      downloadFailed: "Falha no download",
      uploadConfirm: "Enviar e mesclar seus tempos locais para o Google Drive?",
      downloadConfirm:
        "Baixar e mesclar dados do Google Drive com seus tempos locais?",
      downloadMergeConfirm:
        "Isso mesclará os dados do Google Drive com seus tempos locais. Continuar?",
      reloadConfirm: "Recarregar a página para ver as alterações?",
      autoSyncEnabled: "Sincronização automática ativada",
      signInFailed: "Falha no login",
      noSyncFile: "Arquivo de sincronização não encontrado",
      noDataFound: "Nenhum dado encontrado",
      uploadCancelled: "Envio cancelado",
      downloadCancelled: "Download cancelado",
      syncSuccessful: "Sincronização bem-sucedida",
      syncFailed: "Falha na sincronização",
      error: "Erro",
    },
  },
  timerOptions: {
    title: "Opções do Timer",
    warningSounds: "Ativar Sons de Aviso",
    useInspection: "Usar Inspeção WCA (15s)",
    inspectionSound: "Som da Inspeção:",
    inspectionSoundNone: "Nenhum",
    inspectionSoundVoice: "Voz",
    inspectionSoundBeep: "Bip",
    stackmatResetInspection: "Reset do Stackmat Ativa Inspeção",
    stackmatResetNote: "Nota: Só funciona quando o timer não está em 0.000",
    inputTimer: "Modo Timer de Entrada (Inserir tempos manualmente)",
    timerMode: "Modo do Timer:",
    timerModeTimer: "Timer",
    timerModeTyping: "Digitação",
    timerModeStackmat: "Stackmat",
    timerModeBluetooth: "Bluetooth (Em Breve)",
    microphoneInput: "Entrada de Microfone",
    microphoneAuto: "Detecção automática",
    microphoneNote: "Escolha seu divisor Y ou microfone externo",
    decimalPlaces: "Casas Decimais:",
    decimalPlacesNone: "Nenhuma (12)",
    decimalPlaces1: "1 (12.3)",
    decimalPlaces2: "2 (12.34)",
    decimalPlaces3: "3 (12.345)",
  },
  displayOptions: {
    title: "Opções de Exibição",
    showVisualization: "Mostrar Visualização do Puzzle",
    showStats: "Mostrar Estatísticas",
    showDebug: "Mostrar Informações de Debug",
    darkMode: "Modo Escuro",
    showFMCKeyboard: "Mostrar Teclado FMC",
    scrambleFontSize: "Tamanho da Fonte do Embaralhamento",
  },
  app: {
    title: "scTimer",
    description: "Um timer de speedcubing com inspeção WCA e estatísticas",
    enterTime: "Inserir tempo",
    enterSolveTime: "Inserir tempo de resolução manualmente",
    generateScrambles: "Gerar Embaralhamentos",
    outOf: "De:",
    numberOfCubes: "Número de cubos (mínimo 2):",
    numberOfCubesSolved: "Número de cubos resolvidos:",
  },
  timer: {
    ready: "Pronto",
    running: "Executando",
    idle: "Inativo",
    inspection: "Inspeção",
    holding: "Segurando",
  },
  stats: {
    title: "Estatísticas",
    best: "Melhor",
    worst: "Pior",
    mean: "Média",
    avg5: "ao5",
    avg12: "ao12",
    mean3: "mo3",
    bestMo3: "Melhor mo3",
    avg100: "ao100",
    avg1000: "ao1000",
    solves: "Resoluções",
    attempts: "Tentativas",
    moreStats: "Mais Estatísticas",
  },
  statsDetails: {
    title: "Detalhes das Estatísticas",
    titleFor: "Detalhes das Estatísticas para",
    overview: "Visão Geral",
    averages: "Médias",
    records: "Recordes",
    timeDistribution: "Distribuição de Tempos",
    progressChart: "Gráfico de Progresso",
    sessionAnalysis: "Análise da Sessão",
    predictions: "Previsões",
    standardDeviation: "Desvio Padrão",
    bestSingle: "Melhor Single",
    bestAo5: "Melhor ao5",
    bestAo12: "Melhor ao12",
    bestAo100: "Melhor ao100",
    bestAo1000: "Melhor ao1000",
    totalTime: "Tempo Total",
    averageTime: "Tempo Médio",
    solvesPerHour: "Resoluções/Hora",
    consistency: "Consistência",
    nextAo5: "Próximo Objetivo ao5",
    nextAo12: "Próximo Objetivo ao12",
    improvementRate: "Taxa de Melhoria",
    targetTime: "Tempo Alvo",
    currentSession: "Sessão Atual",
    allSessions: "Todas as Sessões",
    importTimes: "Importar Tempos",
    exportJSON: "Exportar JSON",
    exportCSV: "Exportar CSV",
  },
  solveDetails: {
    title: "Detalhes da Resolução",
    time: "Tempo",
    date: "Data",
    scramble: "Embaralhamento",
    editedScramble: "Embaralhamento Editado",
    copyScramble: "Copiar embaralhamento",
    penalty: "Penalidade",
    none: "Nenhuma",
    comment: "Comentário",
    addComment: "Adicionar um comentário...",
    save: "Salvar",
    share: "Compartilhar",
    plusTwo: "+2",
    dnf: "DNF",
  },
  gestures: {
    scrambleCopied: "Embaralhamento copiado",
    noSolvesToDelete: "Nenhuma resolução para deletar",
    solveDeleted: "Resolução deletada",
    cannotAddPenaltyMBLD:
      "Não é possível adicionar penalidade à resolução MBLD",
    dnfRemoved: "DNF removido",
    dnfAdded: "DNF adicionado",
    plus2Added: "Penalidade +2 adicionada",
    penaltyRemoved: "Penalidade removida",
    newScrambleGenerated: "Novo embaralhamento gerado",
    timesPanelOpened: "Painel de tempos aberto",
  },
  times: {
    title: "Tempos de Resolução",
    clear: "Limpar Tempos",
    close: "Fechar",
    delete: "Deletar tempo",
    confirmClear:
      "Tem certeza de que deseja limpar todos os tempos para este evento?",
    confirmDelete: "Tem certeza de que deseja deletar este tempo?",
  },
  buttons: {
    viewTimes: "Ver Tempos",
    ok: "OK",
    cancel: "Cancelar",
  },
  events: {
    333: "3×3×3",
    222: "2×2×2",
    444: "4×4×4",
    555: "5×5×5",
    666: "6×6×6",
    777: "7×7×7",
    "333bf": "3×3×3 Vendado",
    "333fm": "3×3×3 Menos Movimentos",
    "333oh": "3×3×3 Uma Mão",
    clock: "Clock",
    minx: "Megaminx",
    pyram: "Pyraminx",
    skewb: "Skewb",
    sq1: "Square-1",
    "444bf": "4×4×4 Vendado",
    "555bf": "5×5×5 Vendado",
    "333mbf": "3×3×3 Multi-Vendado",
  },
  mbld: {
    cubeCount: "Cubos",
    solvedCount: "Cubos Resolvidos",
    totalCount: "Total de Cubos",
    totalCubes: "Total de Cubos",
    cubesSolved: "Cubos Resolvidos",
    bestPoints: "Melhores Pontos",
    successRate: "Taxa de Sucesso",
    points: "pontos",
    save: "Salvar Resultado",
    visualizations: "Visualizações Multi-Vendado",
    scrambles: "Embaralhamentos Multi-Vendado",
    enterValidNumber: "Por favor, insira um número válido de cubos resolvidos.",
    noScrambles:
      "Nenhum embaralhamento MBLD disponível. Por favor, selecione primeiro o evento 3×3×3 Multi-Vendado.",
    visualizationNotFound:
      "Modal de visualização não encontrado. Por favor, atualize a página e tente novamente.",
    containerNotFound:
      "Container de visualização não encontrado. Por favor, atualize a página e tente novamente.",
    clickToView:
      "Clique para ver todas as visualizações e embaralhamentos de cubos",
    bestScore: "Melhor Pontuação",
    worstScore: "Pior Pontuação",
    meanScore: "Pontuação Média",
    averageScore: "Pontuação Média",
    attempts: "tentativas",
    totalAttempts: "Total de Tentativas",
    clickToViewScrambles: "Clique para ver todos os embaralhamentos",
    clickToViewScramblesCount: "Clique para ver todos os {0} embaralhamentos",
    setup: "Configuração Multi-Vendado",
    results: "Resultados Multi-Vendado",
    generateScrambles: "Gerar Embaralhamentos",
    saveResult: "Salvar Resultado",
    cubeNumber: "Cubo",
    numberOfCubesMinimum: "Número de cubos (mínimo 2):",
    numberOfCubesSolved: "Número de cubos resolvidos:",
    saveFirst: "Por favor, salve seu resultado primeiro.",
    visualizationsTitle: "Visualizações Multi-Vendado ({0} cubos)",
    timeLimit: "Limite de tempo: {0} minutos",
    timeLimitExceeded: "Limite de tempo excedido. O resultado será DNF.",
    negativePoints: "Pontos negativos. O resultado será DNF.",
  },
  modals: {
    error: "Erro",
    warning: "Aviso",
    info: "Informação",
    confirm: "Confirmar",
    prompt: "Entrada Necessária",
  },
  stackmat: {
    error: "Erro Stackmat",
    noMicrophone:
      "Falha ao iniciar timer Stackmat: Nenhum microfone encontrado. Por favor, conecte um microfone e tente novamente.",
    connected: "Conectado",
    disconnected: "Desconectado",
    settingUp: "Configurando...",
  },
  sessions: {
    newSessionTitle: "Nova Sessão",
    editSessionTitle: "Editar Sessão",
    sessionName: "Nome da Sessão:",
    sessionNamePlaceholder: "Minha Sessão",
    puzzleType: "Tipo de Puzzle:",
    create: "Criar",
    save: "Salvar",
  },
  scramble: {
    loading: "Carregando embaralhamento...",
  },
  debug: {
    timerState: "Estado do Timer: ",
    spaceHeldFor: "Espaço Pressionado Por: ",
    currentEvent: "Evento Atual: ",
    scrambleSource: "Fonte do Embaralhamento: ",
  },
  fmc: {
    title: "Desafio Menos Movimentos",
    info: "Resolva o cubo com o menor número de movimentos possível. Você tem 60 minutos para encontrar uma solução.",
    timeRemaining: "Tempo Restante:",
    scramble: "Embaralhamento:",
    solution: "Solução:",
    moveCount: "Movimentos:",
    moves: "movimentos",
    submit: "Enviar",
    resultTitle: "Resultado FMC",
    resultTime: "Tempo:",
    resultSolution: "Solução:",
    resultOk: "OK",
    solutionPlaceholder:
      "Digite sua solução aqui usando a notação padrão WCA...",
    notationHelp: "Ajuda de Notação:",
    notationHelpContent:
      "Giros de face: U, D, L, R, F, B (com sufixos ' ou 2)<br>Movimentos largos: Uw, Dw, etc.<br>Movimentos de fatia: M, E, S<br>Rotações: x, y, z (não contam no total de movimentos)",
    submitSolution: "Enviar Solução",
    validSolution: "Solução válida",
    invalidNotation: "Notação inválida detectada",
    bestMoves: "Melhores Movimentos",
    worstMoves: "Piores Movimentos",
    meanMoves: "Movimentos Médios",
    bestMo3: "Melhor mo3",
    averageMoves: "Movimentos Médios",
    attempts: "tentativas",
    totalAttempts: "Total de Tentativas",
    tooManyMoves: "A solução excede o limite de 80 movimentos",
    timeExceeded:
      "Limite de tempo excedido. Sua solução será marcada como DNF se não for enviada.",
    confirmClose:
      "Tem certeza de que deseja fechar? Sua tentativa será marcada como DNF.",
    dnfReasonTimeout: "Limite de tempo excedido",
    dnfReasonInvalid: "Notação inválida",
    dnfReasonTooManyMoves: "A solução excede 80 movimentos",
    dnfReasonAbandoned: "Tentativa abandonada",
    confirmSubmit: "Tem certeza de que deseja enviar sua solução?",
    pressToStart: "Pressione espaço para iniciar tentativa FMC",
    solutionAccepted: "Solução aceita",
    clickToViewTwizzle: "Clique no link abaixo para ver a solução no Twizzle",
    viewOnTwizzle: "Ver no Twizzle",
    moveCountLabel: "Número de movimentos:",
    movesHTM: "movimentos (HTM)",
    timeUsedLabel: "Tempo usado:",
    loadingFMC: "carregando FMC",
    generatingScramble: "gerando embaralhamento e preparando interface",
  },
  tutorial: {
    welcomeTitle: "Bem-vindo ao scTimer!",
    welcomeSubtitle: "Seu timer profissional de speedcubing",
    selectLanguage: "Selecionar Idioma:",
    feature1: "Timer Padrão WCA",
    feature2: "Estatísticas Avançadas",
    feature3: "Todos os Eventos WCA",
    feature4: "Gerador de Embaralhamentos",
    welcomeDescription:
      "Gostaria de um tour rápido para aprender como usar o scTimer efetivamente? O tutorial irá guiá-lo através dos recursos principais em apenas alguns passos.",
    skipTutorial: "Pular Tutorial",
    startTour: "Iniciar Tour",
    step1: {
      title: "Exibição do Embaralhamento",
      text: "Isso mostra a sequência de embaralhamento para seu puzzle atual. Cada embaralhamento é gerado aleatoriamente seguindo os padrões WCA.",
    },
    step2: {
      title: "Controles do Timer",
      text: "Pressione e segure a BARRA DE ESPAÇO para começar a cronometragem, solte para começar a resolver. No celular, toque e segure a área do timer. O timer segue os padrões de inspeção WCA.",
    },
    step3: {
      title: "Seletor de Eventos",
      text: "Escolha entre todos os eventos WCA incluindo 3x3x3, 2x2x2, 4x4x4 e muitos outros tipos de puzzles. Clique ou toque para abrir o menu suspenso.",
    },
    step4: {
      title: "Acompanhamento de Estatísticas",
      text: "Acompanhe seu progresso com estatísticas detalhadas incluindo melhor tempo, médias de 5, 12 e 100 resoluções. Clique em qualquer estatística para ver mais detalhes.",
    },
    step5: {
      title: "Gerar Novo Embaralhamento",
      text: "Gere um novo embaralhamento quando estiver pronto para sua próxima resolução. Atalho de teclado: Pressione N ou clique no ícone de embaralhamento.",
    },
    step6: {
      title: "Configurações e Personalização",
      text: "Personalize sua experiência de timer com tempo de inspeção, opções de som, modos de timer e preferências de exibição. Atalho de teclado: Pressione S.",
    },
    step7: {
      title: "Atalhos de Teclado",
      text: "Domine esses atalhos: BARRA DE ESPAÇO (iniciar/parar timer), N (novo embaralhamento), S (configurações), ESC (fechar modais), Setas (navegar). No celular, use gestos de deslizar!",
    },
    step8: {
      title: "Gestos Móveis",
      text: "Em dispositivos móveis: Deslize para a esquerda para abrir o painel de tempos, deslize para a direita para fechá-lo, toque e segure o timer para iniciar, toque duplo no embaralhamento para copiar. Belisque para dar zoom nas visualizações.",
    },
    step9: {
      title: "Dicas Pro e Recursos",
      text: "Ative o tempo de inspeção nas configurações para prática WCA. Use diferentes sessões para acompanhar vários eventos. Exporte seus tempos para análise. O timer funciona offline como PWA!",
    },
    previous: "Anterior",
    next: "Próximo",
    finish: "Finalizar",
    close: "Fechar",
    stepCounter: "de",
    restartTutorial: "Reiniciar Tutorial",
  },
};

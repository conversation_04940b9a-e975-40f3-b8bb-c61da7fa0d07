// Spanish language file
export default {
  dir: "ltr",
  settings: {
    title: "Configuración",
    save: "Guardar",
    close: "Cerrar",
    language: "Idioma",
  },
  info: {
    title: "Información",
    pwaInstall: {
      title: "Instalar como App",
      description:
        "Instala scTimer como una Aplicación Web Progresiva para la mejor experiencia. Funciona sin conexión y se siente como una aplicación nativa.",
      install: "Instalar App",
      iosTitle: "Instalación iOS/iPad:",
      iosStep1: "1. Toca el botón Compartir",
      iosStep2:
        '2. Despl<PERSON>zate hacia abajo y toca "Añadir a pantalla de inicio"',
      iosStep3: '3. Toca "Añadir" para instalar',
      note: "Disponible en Chrome, Safari y otros navegadores modernos",
    },
    shortcuts: {
      title: "Atajos de Teclado",
      timer: "Controles del Cronómetro",
      spacebar: "Iniciar/detener cronómetro",
      escape: "Cancelar inspección y cerrar modales",
      navigation: "Navegación y Acciones",
      generate: "Generar nueva mezcla",
      list: "Alternar lista de tiempos",
      settings: "Abrir configuración",
      edit: "Editar mezcla actual",
      copy: "Copiar mezcla al portapapeles",
      stats: "Abrir estadísticas detalladas",
      display: "Alternar Pantalla",
      visualization: "Alternar visualización del puzzle",
      statistics: "Alternar visualización de estadísticas",
      darkMode: "Alternar modo oscuro",
      inspection: "Alternar inspección WCA",
      penalties: "Gestión de Penalizaciones",
      removePenalty: "Quitar penalización del último solve",
      addPlus2: "Añadir penalización +2 al último solve",
      addDNF: "Añadir penalización DNF al último solve",
      session: "Gestión de Sesiones",
      emptySession: "Vaciar sesión actual",
      exportSession: "Exportar sesión actual",
      eventSwitching: "Cambio de Eventos",
      alt2to7: "Cambiar a cubos 2×2×2 hasta 7×7×7",
      altP: "Cambiar a Pyraminx",
      altM: "Cambiar a Megaminx",
      altC: "Cambiar a Clock",
      altS: "Cambiar a Skewb",
      alt1: "Cambiar a Square-1",
      altF: "Cambiar a 3×3×3 Menos Movimientos",
      altO: "Cambiar a 3×3×3 Una Mano",
      blindfolded: "Eventos a Ciegas",
      altCtrl3: "Cambiar a 3×3×3 a Ciegas",
      altCtrl4: "Cambiar a 4×4×4 a Ciegas",
      altCtrl5: "Cambiar a 5×5×5 a Ciegas",
      altCtrl6: "Cambiar a 3×3×3 Multi-Ciegas",
      sessionMgmt: "Gestión de Sesiones",
      altN: "Crear nueva sesión",
    },
    gestures: {
      title: "Gestos Móviles",
      swipeDown: "Deslizar Hacia Abajo",
      swipeDownDesc: "Eliminar último solve",
      swipeUp: "Deslizar Hacia Arriba",
      swipeUpDesc: "Alternar penalizaciones (ninguna/+2/DNF)",
      swipeLeft: "Deslizar Hacia la Izquierda",
      swipeLeftDesc: "LTR: Nueva mezcla | RTL: Lista de tiempos",
      swipeRight: "Deslizar Hacia la Derecha",
      swipeRightDesc: "LTR: Lista de tiempos | RTL: Nueva mezcla",
      doubleClick: "Doble Clic",
      doubleClickDesc: "Copiar mezcla actual (PC/Móvil)",
      longPress: "Pulsación Larga/Clic y Mantener",
      longPressDesc: "Editar mezcla actual (PC/Móvil)",
    },
    features: {
      title: "Características Principales",
      timer: "Cronómetro Profesional",
      timerDesc: "Cronometraje compatible con WCA con modo de inspección",
      puzzles: "Todos los Eventos WCA",
      puzzlesDesc: "Soporte completo para todos los eventos oficiales de WCA",
      statistics: "Estadísticas Avanzadas",
      statisticsDesc: "Análisis detallado con ao5, ao12, ao100",
      scrambles: "Mezclas Oficiales",
      scramblesDesc: "Generación de mezclas estándar WCA con visualización 2D",
      multilingual: "Soporte Multiidioma",
      multilingualDesc: "15+ idiomas con soporte RTL",
      sync: "Sincronización Google Drive",
      syncDesc: "Sincronización entre dispositivos con fusión inteligente",
    },
    sync: {
      title: "Sincronización Google Drive",
      description:
        "Sincroniza tus tiempos de solve en todos los dispositivos usando Google Drive. Tus datos se almacenan de forma segura en tu cuenta personal de Google Drive.",
      secure: "Seguro y Privado",
      automatic: "Sincronización Automática",
      offline: "Soporte Sin Conexión",
      smartMerge: "Fusión Inteligente",
      note: "Habilita la sincronización de Google Drive en Configuración para mantener tus tiempos sincronizados en todos tus dispositivos.",
      status: "Estado:",
      notConnected: "No conectado",
      connected: "Conectado",
      connect: "Conectar",
      disconnect: "Desconectar",
      upload: "Subir a Drive",
      download: "Descargar de Drive",
      autoSync: "Sincronización Automática",
      autoSyncNote:
        "Sincroniza automáticamente tus tiempos cuando estés conectado a Internet",
      uploading: "Subiendo...",
      downloading: "Descargando...",
      syncing: "Sincronizando...",
      uploadSuccess: "Subida exitosa",
      downloadSuccess: "Descarga exitosa",
      uploadFailed: "Subida fallida",
      downloadFailed: "Descarga fallida",
      uploadConfirm: "¿Subir y fusionar tus tiempos locales a Google Drive?",
      downloadConfirm:
        "¿Descargar y fusionar datos de Google Drive con tus tiempos locales?",
      downloadMergeConfirm:
        "Esto fusionará los datos de Google Drive con tus tiempos locales. ¿Continuar?",
      reloadConfirm: "¿Recargar la página para ver los cambios?",
      autoSyncEnabled: "Sincronización automática habilitada",
      signInFailed: "Error al iniciar sesión",
      noSyncFile: "No se encontró archivo de sincronización",
      noDataFound: "No se encontraron datos",
      uploadCancelled: "Subida cancelada",
      downloadCancelled: "Descarga cancelada",
      syncSuccessful: "Sincronización exitosa",
      syncFailed: "Sincronización fallida",
      error: "Error",
    },
  },
  timerOptions: {
    title: "Opciones del Cronómetro",
    warningSounds: "Habilitar Sonidos de Advertencia",
    useInspection: "Usar Inspección WCA (15s)",
    inspectionSound: "Sonido de Inspección:",
    inspectionSoundNone: "Ninguno",
    inspectionSoundVoice: "Voz",
    inspectionSoundBeep: "Pitido",
    stackmatResetInspection: "Reset de Stackmat Activa Inspección",
    stackmatResetNote:
      "Nota: Solo funciona cuando el cronómetro no está en 0.000",
    inputTimer: "Modo Cronómetro de Escritura (Introducir tiempos manualmente)",
    timerMode: "Modo Cronómetro:",
    timerModeTimer: "Cronómetro",
    timerModeTyping: "Escritura",
    timerModeStackmat: "Stackmat",
    timerModeBluetooth: "Bluetooth (Próximamente)",
    microphoneInput: "Entrada de Micrófono",
    microphoneAuto: "Detección automática",
    microphoneNote: "Elige tu divisor Y o micrófono externo",
    decimalPlaces: "Decimales:",
    decimalPlacesNone: "Ninguno (12)",
    decimalPlaces1: "1 (12.3)",
    decimalPlaces2: "2 (12.34)",
    decimalPlaces3: "3 (12.345)",
  },
  displayOptions: {
    title: "Opciones de Pantalla",
    showVisualization: "Mostrar Visualización del Puzzle",
    showStats: "Mostrar Estadísticas",
    showDebug: "Mostrar Información de Depuración",
    darkMode: "Modo Oscuro",
    showFMCKeyboard: "Mostrar Teclado FMC",
    scrambleFontSize: "Tamaño de Fuente de Mezcla",
  },
  app: {
    title: "scTimer",
    description:
      "Un cronómetro de speedcubing con inspección WCA y estadísticas",
    enterTime: "Introducir tiempo",
    enterSolveTime: "Introducir tiempo de solve manualmente",
    generateScrambles: "Generar Mezclas",
    outOf: "De:",
    numberOfCubes: "Número de cubos (mínimo 2):",
    numberOfCubesSolved: "Número de cubos resueltos:",
  },
  timer: {
    ready: "Listo",
    running: "Ejecutándose",
    idle: "Inactivo",
    inspection: "Inspección",
    holding: "Manteniendo",
  },
  stats: {
    title: "Estadísticas",
    best: "Mejor",
    worst: "Peor",
    mean: "Media",
    avg5: "ao5",
    avg12: "ao12",
    mean3: "mo3",
    bestMo3: "Mejor mo3",
    avg100: "ao100",
    avg1000: "ao1000",
    solves: "Solves",
    attempts: "Intentos",
    moreStats: "Más Estadísticas",
  },
  statsDetails: {
    title: "Detalles de Estadísticas",
    titleFor: "Detalles de Estadísticas para",
    overview: "Resumen",
    averages: "Promedios",
    records: "Récords",
    timeDistribution: "Distribución de Tiempos",
    progressChart: "Progreso en el Tiempo",
    sessionAnalysis: "Análisis de Sesión",
    predictions: "Predicciones",
    standardDeviation: "Desv. Estándar",
    bestSingle: "Mejor Individual",
    bestAo5: "Mejor ao5",
    bestAo12: "Mejor ao12",
    bestAo100: "Mejor ao100",
    bestAo1000: "Mejor ao1000",
    totalTime: "Tiempo Total",
    averageTime: "Tiempo Promedio",
    solvesPerHour: "Solves/Hora",
    consistency: "Consistencia",
    nextAo5: "Objetivo Próximo ao5",
    nextAo12: "Objetivo Próximo ao12",
    improvementRate: "Tasa de Mejora",
    targetTime: "Tiempo Objetivo",
    currentSession: "Sesión Actual",
    allSessions: "Todas las Sesiones",
    importTimes: "Importar Tiempos",
    exportJSON: "Exportar JSON",
    exportCSV: "Exportar CSV",
  },
  solveDetails: {
    title: "Detalles del Solve",
    time: "Tiempo",
    date: "Fecha",
    scramble: "Mezcla",
    editedScramble: "Mezcla Editada",
    copyScramble: "Copiar mezcla",
    penalty: "Penalización",
    none: "Ninguna",
    comment: "Comentario",
    addComment: "Añadir un comentario...",
    save: "Guardar",
    share: "Compartir",
    plusTwo: "+2",
    dnf: "DNF",
  },
  gestures: {
    scrambleCopied: "Mezcla copiada",
    noSolvesToDelete: "No hay solves para eliminar",
    solveDeleted: "Solve eliminado",
    cannotAddPenaltyMBLD: "No se puede añadir penalización a solve MBLD",
    dnfRemoved: "DNF eliminado",
    dnfAdded: "DNF añadido",
    plus2Added: "Penalización +2 añadida",
    penaltyRemoved: "Penalización eliminada",
    newScrambleGenerated: "Nueva mezcla generada",
    timesPanelOpened: "Panel de tiempos abierto",
  },
  times: {
    title: "Tiempos de Solve",
    clear: "Limpiar Tiempos",
    close: "Cerrar",
    delete: "Eliminar tiempo",
    confirmClear:
      "¿Estás seguro de que quieres limpiar todos los tiempos para este evento?",
    confirmDelete: "¿Estás seguro de que quieres eliminar este tiempo?",
  },
  buttons: {
    viewTimes: "Ver Tiempos",
    ok: "OK",
    cancel: "Cancelar",
  },
  events: {
    333: "3×3×3",
    222: "2×2×2",
    444: "4×4×4",
    555: "5×5×5",
    666: "6×6×6",
    777: "7×7×7",
    "333bf": "3×3×3 a Ciegas",
    "333fm": "3×3×3 Menos Movimientos",
    "333oh": "3×3×3 Una Mano",
    clock: "Clock",
    minx: "Megaminx",
    pyram: "Pyraminx",
    skewb: "Skewb",
    sq1: "Square-1",
    "444bf": "4×4×4 a Ciegas",
    "555bf": "5×5×5 a Ciegas",
    "333mbf": "3×3×3 Multi-Ciegas",
  },
  mbld: {
    cubeCount: "Cubos",
    solvedCount: "Cubos Resueltos",
    totalCount: "Cubos Totales",
    totalCubes: "Cubos Totales",
    cubesSolved: "Cubos Resueltos",
    bestPoints: "Mejores Puntos",
    successRate: "Tasa de Éxito",
    points: "puntos",
    save: "Guardar Resultado",
    visualizations: "Visualizaciones Multi-Ciegas",
    scrambles: "Mezclas Multi-Ciegas",
    enterValidNumber:
      "Por favor introduce un número válido de cubos resueltos.",
    noScrambles:
      "No hay mezclas MBLD disponibles. Por favor selecciona primero el evento 3×3×3 Multi-Ciegas.",
    visualizationNotFound:
      "Modal de visualización no encontrado. Por favor recarga la página e inténtalo de nuevo.",
    containerNotFound:
      "Contenedor de visualización no encontrado. Por favor recarga la página e inténtalo de nuevo.",
    clickToView:
      "Haz clic para ver todas las visualizaciones y mezclas de cubos",
    bestScore: "Mejor Puntuación",
    worstScore: "Peor Puntuación",
    meanScore: "Puntuación Media",
    averageScore: "Puntuación Promedio",
    attempts: "intentos",
    totalAttempts: "Intentos Totales",
    clickToViewScrambles: "Haz clic para ver todas las mezclas",
    clickToViewScramblesCount: "Haz clic para ver todas las {0} mezclas",
    setup: "Configuración Multi-Ciegas",
    results: "Resultados Multi-Ciegas",
    generateScrambles: "Generar Mezclas",
    saveResult: "Guardar Resultado",
    cubeNumber: "Cubo",
    numberOfCubesMinimum: "Número de cubos (mínimo 2):",
    numberOfCubesSolved: "Número de cubos resueltos:",
    saveFirst: "Por favor guarda tu resultado primero.",
    visualizationsTitle: "Visualizaciones Multi-Ciegas ({0} cubos)",
    timeLimit: "Límite de tiempo: {0} minutos",
    timeLimitExceeded: "Límite de tiempo excedido. El resultado será DNF.",
    negativePoints: "Puntos negativos. El resultado será DNF.",
  },
  modals: {
    error: "Error",
    warning: "Advertencia",
    info: "Información",
    confirm: "Confirmar",
    prompt: "Entrada Requerida",
  },
  stackmat: {
    error: "Error de Stackmat",
    noMicrophone:
      "Error al iniciar cronómetro Stackmat: No se encontró micrófono. Por favor conecta un micrófono e inténtalo de nuevo.",
    connected: "Conectado",
    disconnected: "Desconectado",
    settingUp: "Configurando...",
  },
  sessions: {
    newSessionTitle: "Nueva Sesión",
    editSessionTitle: "Editar Sesión",
    sessionName: "Nombre de Sesión:",
    sessionNamePlaceholder: "Mi Sesión",
    puzzleType: "Tipo de Puzzle:",
    create: "Crear",
    save: "Guardar",
  },
  scramble: {
    loading: "Cargando mezcla...",
  },
  debug: {
    timerState: "Estado del Cronómetro: ",
    spaceHeldFor: "Espacio Mantenido Por: ",
    currentEvent: "Evento Actual: ",
    scrambleSource: "Fuente de Mezcla: ",
  },
  fmc: {
    title: "Desafío de Menos Movimientos",
    info: "Resuelve el cubo en el menor número de movimientos posible. Tienes 60 minutos para encontrar una solución.",
    timeRemaining: "Tiempo:",
    scramble: "Mezcla:",
    solution: "Solución:",
    moveCount: "Movimientos:",
    moves: "movimientos",
    submit: "Enviar",
    resultTitle: "Resultado FMC",
    resultTime: "Tiempo:",
    resultSolution: "Solución:",
    resultOk: "OK",
    solutionPlaceholder:
      "Introduce tu solución aquí usando la notación estándar WCA...",
    notationHelp: "Ayuda de Notación:",
    notationHelpContent:
      "Giros de cara: U, D, L, R, F, B (con sufijos ' o 2)<br>Movimientos amplios: Uw, Dw, etc.<br>Movimientos de corte: M, E, S<br>Rotaciones: x, y, z (no cuentan en el total de movimientos)",
    submitSolution: "Enviar Solución",
    validSolution: "Solución válida",
    invalidNotation: "Notación inválida detectada",
    bestMoves: "Mejores Movimientos",
    worstMoves: "Peores Movimientos",
    meanMoves: "Movimientos Promedio",
    bestMo3: "Mejor mo3",
    averageMoves: "Movimientos Promedio",
    attempts: "intentos",
    totalAttempts: "Intentos Totales",
    tooManyMoves: "La solución excede el límite de 80 movimientos",
    timeExceeded:
      "Límite de tiempo excedido. Tu solución será marcada como DNF si no se envía.",
    confirmClose:
      "¿Estás seguro de que quieres cerrar? Tu intento será marcado como DNF.",
    dnfReasonTimeout: "Límite de tiempo excedido",
    dnfReasonInvalid: "Notación inválida",
    dnfReasonTooManyMoves: "La solución excede 80 movimientos",
    dnfReasonAbandoned: "Intento abandonado",
    confirmSubmit: "¿Estás seguro de que quieres enviar tu solución?",
    pressToStart: "Presiona espacio para iniciar intento FMC",
    solutionAccepted: "Solución aceptada",
    clickToViewTwizzle:
      "Haz clic en el enlace de abajo para ver la solución en Twizzle",
    viewOnTwizzle: "Ver en Twizzle",
    moveCountLabel: "Número de movimientos:",
    movesHTM: "movimientos (HTM)",
    timeUsedLabel: "Tiempo usado:",
    loadingFMC: "cargando FMC",
    generatingScramble: "generando mezcla y preparando interfaz",
  },
  tutorial: {
    welcomeTitle: "¡Bienvenido a scTimer!",
    welcomeSubtitle: "Tu cronómetro profesional de speedcubing",
    selectLanguage: "Seleccionar Idioma:",
    feature1: "Cronómetro Estándar WCA",
    feature2: "Estadísticas Avanzadas",
    feature3: "Todos los Eventos WCA",
    feature4: "Generador de Mezclas",
    welcomeDescription:
      "¿Te gustaría un recorrido rápido para aprender a usar scTimer de manera efectiva? El tutorial te guiará a través de las características principales en solo unos pocos pasos.",
    skipTutorial: "Saltar Tutorial",
    startTour: "Iniciar Recorrido",
    step1: {
      title: "Pantalla de Mezcla",
      text: "Esto muestra la secuencia de mezcla para tu puzzle actual. Cada mezcla se genera aleatoriamente siguiendo los estándares WCA.",
    },
    step2: {
      title: "Controles del Cronómetro",
      text: "Presiona y mantén la BARRA ESPACIADORA para comenzar a cronometrar, suelta para empezar a resolver. En móvil, toca y mantén el área del cronómetro. El cronómetro sigue los estándares de inspección WCA.",
    },
    step3: {
      title: "Selector de Eventos",
      text: "Elige entre todos los eventos WCA incluyendo 3x3x3, 2x2x2, 4x4x4, y muchos más tipos de puzzles. Haz clic o toca para abrir el menú desplegable.",
    },
    step4: {
      title: "Seguimiento de Estadísticas",
      text: "Rastrea tu progreso con estadísticas detalladas incluyendo mejor tiempo, promedios de 5, 12, y 100 solves. Haz clic en cualquier estadística para ver más detalles.",
    },
    step5: {
      title: "Generar Nueva Mezcla",
      text: "Genera una nueva mezcla cuando estés listo para tu próximo solve. Atajo de teclado: Presiona N o haz clic en el ícono de mezcla.",
    },
    step6: {
      title: "Configuración y Personalización",
      text: "Personaliza tu experiencia de cronómetro con tiempo de inspección, opciones de sonido, modos de cronómetro, y preferencias de pantalla. Atajo de teclado: Presiona S.",
    },
    step7: {
      title: "Atajos de Teclado",
      text: "Domina estos atajos: BARRA ESPACIADORA (iniciar/detener cronómetro), N (nueva mezcla), S (configuración), ESC (cerrar modales), Teclas de flecha (navegar). ¡En móvil, usa gestos de deslizamiento!",
    },
    step8: {
      title: "Gestos Móviles",
      text: "En dispositivos móviles: Desliza a la izquierda para abrir el panel de tiempos, desliza a la derecha para cerrarlo, toca y mantén el cronómetro para iniciar, doble toque en la mezcla para copiarla. Pellizca para hacer zoom en las visualizaciones.",
    },
    step9: {
      title: "Consejos Pro y Características",
      text: "Habilita el tiempo de inspección en configuración para práctica WCA. Usa diferentes sesiones para rastrear varios eventos. Exporta tus tiempos para análisis. ¡El cronómetro funciona sin conexión como PWA!",
    },
    previous: "Anterior",
    next: "Siguiente",
    finish: "Finalizar",
    close: "Cerrar",
    stepCounter: "de",
    restartTutorial: "Reiniciar Tutorial",
  },
};

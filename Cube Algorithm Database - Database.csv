ID,Puzzle_Name,Event_Type,Category,Method,Sub-Method/Step,Case_Name,Sub_Case_Name,Standard_Algorithm,Alternative_Algorithm_1,Alternative_Algorithm_2,User_Edited_Algorithm,Move_Count,Difficulty_Rating,Learning_Status,Mark_Type,Notes,Source,Last_Updated
1,3x3x3 Cube,WCA,NxNxN Cube,CFOP,OLL,Dot Case,1,(R U2 R') (R' F R F') U2 (R' F R F'),R' U' F R' F' R2 U R f' U' f,,,12,2,,,,https://speedcubedb.com/a/3x3/OLL,
2,3x3x3 Cube,WCA,NxNxN Cube,CFOP,OLL,Dot Case,2,F (R U R' U') F' f (R U R' U') f',y r U r' U2 R U2 R' U2 r U' r',,,12,2,,,,https://speedcubedb.com/a/3x3/OLL,
3,3x3x3 Cube,WCA,NxNxN Cube,CF<PERSON>,<PERSON>LL,<PERSON> Case,3,y' f (R U R' U') f' (U') F (R U R' U') F',r' R2 U R' U r U2 r' U M',,,14,3,,,,https://speedcubedb.com/a/3x3/OLL,
4,3x3x3 Cube,WCA,NxNxN Cube,CFOP,OLL,Dot Case,4,y' f (R U R' U') f' (U) F (R U R' U') F',l L2 U' L U' l' U2 l U' M',,,14,3,,,,https://speedcubedb.com/a/3x3/OLL,
5,3x3x3 Cube,WCA,NxNxN Cube,CFOP,OLL,Dot Case,17,(R U R' U) (R' F R F') U2 (R' F R F'),y2 F R' F' R U S' R U' R' S,,,13,2,,,,https://speedcubedb.com/a/3x3/OLL,
6,3x3x3 Cube,WCA,NxNxN Cube,CFOP,OLL,Dot Case,18,y (R U2 R') (R' F R F') U2 M' (U R U' r'),r U R' U R U2 r2 U' R U' R' U2 r,,,14,3,,,,https://speedcubedb.com/a/3x3/OLL,
7,3x3x3 Cube,WCA,NxNxN Cube,CFOP,OLL,Dot Case,19,M U (R U R' U') M' (R' F R F'),r' R U R U R' U' r R2 F R F',,,11,2,,,,https://speedcubedb.com/a/3x3/OLL,
8,3x3x3 Cube,WCA,NxNxN Cube,CFOP,OLL,Dot Case,20,(r U R' U') M2 (U R U' R') U' M',M' U2 M U2 M' U M U2 M' U2 M,,,11,2,,,,https://speedcubedb.com/a/3x3/OLL,
9,3x3x3 Cube,WCA,NxNxN Cube,CFOP,OLL,Square Shape,5,r' U2 (R U R' U) r,y2 l' U2 L U L' U l,,,7,1,,,,https://speedcubedb.com/a/3x3/OLL,
10,3x3x3 Cube,WCA,NxNxN Cube,CFOP,OLL,Square Shape,6,r U2 (R' U' R U') r',y2 l U2 L' U' L U' l',,,7,1,,,,https://speedcubedb.com/a/3x3/OLL,
11,3x3x3 Cube,WCA,NxNxN Cube,CFOP,OLL,Lightning Shape,7,r (U R' U R) U2 r',r U r' U R U' R' r U' r',,,7,1,,,,https://speedcubedb.com/a/3x3/OLL,
12,3x3x3 Cube,WCA,NxNxN Cube,CFOP,OLL,Lightning Shape,8,y2 r' (U' R U' R') U2 r,l' U' L U' L' U2 l,,,8,2,,,,https://speedcubedb.com/a/3x3/OLL,
13,3x3x3 Cube,WCA,NxNxN Cube,CFOP,OLL,Lightning Shape,11,M (R U R' U R U2 R') U M',y2 r U R' U R' F R F' R U2 r',,,10,2,,,,https://speedcubedb.com/a/3x3/OLL,
14,3x3x3 Cube,WCA,NxNxN Cube,CFOP,OLL,Lightning Shape,12,y' M' (R' U' R U' R' U2 R) U' M,y l L2 U' L U' L' U2 L U' M',,,11,2,,,,https://speedcubedb.com/a/3x3/OLL,
15,3x3x3 Cube,WCA,NxNxN Cube,CFOP,OLL,Lightning Shape,39,y L F' (L' U' L U) F U' L',y' f' L F L' U' L' U L S,,,10,2,,,,https://speedcubedb.com/a/3x3/OLL,
16,3x3x3 Cube,WCA,NxNxN Cube,CFOP,OLL,Lightning Shape,40,y R' F (R U R' U') F' U R,y' f R' F' R U R U' R' S',,,10,2,,,,https://speedcubedb.com/a/3x3/OLL,
17,3x3x3 Cube,WCA,NxNxN Cube,CFOP,OLL,Fish Shape,9,y (R U R' U') (R' F R) (R U R' U') F',R U2 R' U' S' R U' R' S,,,13,2,,,,https://speedcubedb.com/a/3x3/OLL,
18,3x3x3 Cube,WCA,NxNxN Cube,CFOP,OLL,Fish Shape,10,(R U R' U) (R' F R F') (R U2 R'),y F U F' R' F R U' R' F' R,,,11,2,,,,https://speedcubedb.com/a/3x3/OLL,
19,3x3x3 Cube,WCA,NxNxN Cube,CFOP,OLL,Fish Shape,35,(R U2 R') (R' F R F') (R U2 R'),f R U R' U' f' R U R' U R U2 R',,,10,2,,,,https://speedcubedb.com/a/3x3/OLL,
20,3x3x3 Cube,WCA,NxNxN Cube,CFOP,OLL,Fish Shape,37,F R (U' R' U') (R U R') F',F R U' R' U' R U R' F',,,9,1,,,,https://speedcubedb.com/a/3x3/OLL,
21,3x3x3 Cube,WCA,NxNxN Cube,CFOP,OLL,Knight Move Shape,13,(r U' r') U' (r U r') (F' U F),F U R U' R2 F' R U R U' R',,,10,2,,,,https://speedcubedb.com/a/3x3/OLL,
22,3x3x3 Cube,WCA,NxNxN Cube,CFOP,OLL,Knight Move Shape,14,R' F (R U R') F' R (F U' F'),r U R' U' r' F R2 U R' U' F',,,10,2,,,,https://speedcubedb.com/a/3x3/OLL,
23,3x3x3 Cube,WCA,NxNxN Cube,CFOP,OLL,Knight Move Shape,15,(r' U' r) (R' U' R U) (r' U r),y2 l' U' l L' U' L U l' U l,,,10,2,,,,https://speedcubedb.com/a/3x3/OLL,
24,3x3x3 Cube,WCA,NxNxN Cube,CFOP,OLL,Knight Move Shape,16,(r U r') (R U R' U') (r U' r'),y2 R' F R U R' U' F' R U' R' U2 R,,,10,2,,,,https://speedcubedb.com/a/3x3/OLL,
25,3x3x3 Cube,WCA,NxNxN Cube,CFOP,OLL,C Shape,34,(r U r') (R U R' U') (r U' r'),y f R f' U' r' U' R U M',,,10,2,,,,https://speedcubedb.com/a/3x3/OLL,
26,3x3x3 Cube,WCA,NxNxN Cube,CFOP,OLL,C Shape,46,R' U' (R' F R F') U R,R' F' U' F R U' R' U2 R,,,8,1,,,,https://speedcubedb.com/a/3x3/OLL,
27,3x3x3 Cube,WCA,NxNxN Cube,CFOP,OLL,Awkward Shape,29,y (R U R') U' (R U' R') (F' U' F) (R U R'),r2 D' r U r' D r2 U' r' U' r,,,14,3,,,,https://speedcubedb.com/a/3x3/OLL,
28,3x3x3 Cube,WCA,NxNxN Cube,CFOP,OLL,Awkward Shape,30,y2 F U (R U2 R') U' (R U2 R') U' F',y2 F R' F R2 U' R' U' R U R' F2,,,12,2,,,,https://speedcubedb.com/a/3x3/OLL,
29,3x3x3 Cube,WCA,NxNxN Cube,CFOP,OLL,Awkward Shape,41,y2 (R U R' U) (R U2 R') F (R U R' U') F',y2 F U R2 D R' U' R D' R2 F',,,14,3,,,,https://speedcubedb.com/a/3x3/OLL,
30,3x3x3 Cube,WCA,NxNxN Cube,CFOP,OLL,Awkward Shape,42,(R' U' R U') (R' U2 R) F (R U R' U') F',y F R' F' R U2 R' U' R2 U' R2 U2 R,,,13,2,,,,https://speedcubedb.com/a/3x3/OLL,
31,3x3x3 Cube,WCA,NxNxN Cube,CFOP,OLL,All Corners Oriented,28,(r U R' U') M (U R U' R'),R' F R S R' F' R S',,,9,1,,,,https://speedcubedb.com/a/3x3/OLL,
32,3x3x3 Cube,WCA,NxNxN Cube,CFOP,OLL,All Corners Oriented,57,(R U R' U') M' (U R U' r'),y R U' R' S' R U R' S,,,9,2,,,,https://speedcubedb.com/a/3x3/OLL,
33,3x3x3 Cube,WCA,NxNxN Cube,CFOP,OLL,L Shapes,47,F' (L' U' L U) (L' U' L U) F,y' F R' F' R U2 R U' R' U R U2 R',,,10,2,,,,https://speedcubedb.com/a/3x3/OLL,
34,3x3x3 Cube,WCA,NxNxN Cube,CFOP,OLL,L Shapes,48,F (R U R' U') (R U R' U') F',F R' F' U2 R U R' U R2 U2 R',,,10,2,,,,https://speedcubedb.com/a/3x3/OLL,
35,3x3x3 Cube,WCA,NxNxN Cube,CFOP,OLL,L Shapes,49,y2 r U' (r2 U) (r2 U) (r2) U' r,l U' l2 U l2 U l2 U' l,,,10,2,,,,https://speedcubedb.com/a/3x3/OLL,
36,3x3x3 Cube,WCA,NxNxN Cube,CFOP,OLL,L Shapes,50,r' U (r2 U') (r2 U') (r2) U r',y2 l' U l2 U' l2 U' l2 U l',,,9,1,,,,https://speedcubedb.com/a/3x3/OLL,
37,3x3x3 Cube,WCA,NxNxN Cube,CFOP,OLL,L Shapes,53,(r' U' R U') (R' U R U') (R' U2 r),y2 l' U' L U' L' U L U' L' U2 l,,,11,2,,,,https://speedcubedb.com/a/3x3/OLL,
38,3x3x3 Cube,WCA,NxNxN Cube,CFOP,OLL,L Shapes,54,(r U R' U) (R U' R' U) (R U2 r'),y2 l U L' U L U' L' U L U2 l',,,11,2,,,,https://speedcubedb.com/a/3x3/OLL,
39,3x3x3 Cube,WCA,NxNxN Cube,CFOP,OLL,Line Shapes,51,f (R U R' U') (R U R' U') f',F' U' L' U L U' L' U L F,,,10,2,,,,https://speedcubedb.com/a/3x3/OLL,
40,3x3x3 Cube,WCA,NxNxN Cube,CFOP,OLL,Line Shapes,52,y2 R' (F' U' F U') (R U R' U) R,R U R' U R U' B U' B' R',,,11,2,,,,https://speedcubedb.com/a/3x3/OLL,
41,3x3x3 Cube,WCA,NxNxN Cube,CFOP,OLL,Line Shapes,55,R U2 R2 (U' R U' R') U2 (F R F'),y R' F U R U' R2 F' R2 U R' U' R,,,11,2,,,,https://speedcubedb.com/a/3x3/OLL,
42,3x3x3 Cube,WCA,NxNxN Cube,CFOP,OLL,Line Shapes,56,(r U r') (U R U' R') (U R U' R') (r U' r'),F R U R' U' R F' r U R' U' r',,,14,2,,,,https://speedcubedb.com/a/3x3/OLL,
43,3x3x3 Cube,WCA,NxNxN Cube,CFOP,OLL,OCLL,21,(R U R' U) (R U' R' U) (R U2 R'),y F R U R' U' R U R' U' R U R' U' F',,,11,2,,,,https://speedcubedb.com/a/3x3/OLL,
44,3x3x3 Cube,WCA,NxNxN Cube,CFOP,OLL,OCLL,22,R U2 (R2' U') (R2 U') (R2' U') U' R,R' U2 R2 U R2 U R2 U2 R',,,10,1,,,,https://speedcubedb.com/a/3x3/OLL,
45,3x3x3 Cube,WCA,NxNxN Cube,CFOP,OLL,OCLL,23,R2 D (R' U2 R) D' (R' U2 R'),y2 R2 D' R U2 R' D R U2 R,,,9,2,,,,https://speedcubedb.com/a/3x3/OLL,
46,3x3x3 Cube,WCA,NxNxN Cube,CFOP,OLL,OCLL,24,(r U R' U') (r' F R F'),L F R' F' L' F R F',,,8,1,,,,https://speedcubedb.com/a/3x3/OLL,
47,3x3x3 Cube,WCA,NxNxN Cube,CFOP,OLL,OCLL,25,y (F' r U R') (U' r' F R),R U2 R D R' U2 R D' R2,,,9,2,,,,https://speedcubedb.com/a/3x3/OLL,
48,3x3x3 Cube,WCA,NxNxN Cube,CFOP,OLL,OCLL,26,y R U2 (R' U' R U') R',R' U' R U' R' U2 R,,,8,2,,,,https://speedcubedb.com/a/3x3/OLL,
49,3x3x3 Cube,WCA,NxNxN Cube,CFOP,OLL,OCLL,27,(R U R' U) (R U2 R'),y2 L U L' U L U2 L',,,7,1,,,,https://speedcubedb.com/a/3x3/OLL,
50,3x3x3 Cube,WCA,NxNxN Cube,CFOP,OLL,P Shapes,31,(R' U' F) (U R U' R') F' R,y' F R' F' R U R U R' U' R U' R',,,9,1,,,,https://speedcubedb.com/a/3x3/OLL,
51,3x3x3 Cube,WCA,NxNxN Cube,CFOP,OLL,P Shapes,32,S (R U R' U') (R' F R f'),y2 L U F' U' L' U L F L',,,9,2,,,,https://speedcubedb.com/a/3x3/OLL,
52,3x3x3 Cube,WCA,NxNxN Cube,CFOP,OLL,P Shapes,43,y R' U' (F' U F) R,y2 F' U' L' U L F,,,7,2,,,,https://speedcubedb.com/a/3x3/OLL,
53,3x3x3 Cube,WCA,NxNxN Cube,CFOP,OLL,P Shapes,44,f (R U R' U') f',y2 F U R U' R' F',,,6,1,,,,https://speedcubedb.com/a/3x3/OLL,
54,3x3x3 Cube,WCA,NxNxN Cube,CFOP,OLL,T Shapes,33,(R U R' U') (R' F R F'),y2 L' U' L U L F' L' F,,,8,1,,,,https://speedcubedb.com/a/3x3/OLL,
55,3x3x3 Cube,WCA,NxNxN Cube,CFOP,OLL,T Shapes,45,F (R U R' U') F',y2 F' L' U' L U F,,,6,1,,,,https://speedcubedb.com/a/3x3/OLL,
56,3x3x3 Cube,WCA,NxNxN Cube,CFOP,OLL,W Shape,36,y2 (L' U' L U') (L' U L U) (L F' L' F),y2 L' U' L U' L' U L U L F' L' F,,,13,2,,,,https://speedcubedb.com/a/3x3/OLL,
57,3x3x3 Cube,WCA,NxNxN Cube,CFOP,OLL,W Shape,38,(R U R' U) (R U' R' U') (R' F R F'),y2 L' U2 l' D' l U2 l' D l L,,,12,2,,,,https://speedcubedb.com/a/3x3/OLL,
,,,,,,,,,,,,,,,,,,
58,3x3x3 Cube,WCA,NxNxN Cube,CFOP,PLL,Aa Perm,,x (R' U R') D2 (R U' R') D2 R2 x',,,,11,3,,,,https://speedcubedb.com/a/3x3/PLL,
59,3x3x3 Cube,WCA,NxNxN Cube,CFOP,PLL,Ab Perm,,x R2 D2 (R U R') D2 (R U' R) x',,,,11,3,,,,https://speedcubedb.com/a/3x3/PLL,
60,3x3x3 Cube,WCA,NxNxN Cube,CFOP,PLL,E Perm ,,y x' (R U' R' D) (R U R' D') (R U R' D) (R U' R' D') x,,,,19,4,,,,https://speedcubedb.com/a/3x3/PLL,
61,3x3x3 Cube,WCA,NxNxN Cube,CFOP,PLL,F Perm ,,y (R' U' F') (R U R' U') R' F R2 (U' R' U') (R U R' U) R,,,,19,3,,,,https://speedcubedb.com/a/3x3/PLL,
62,3x3x3 Cube,WCA,NxNxN Cube,CFOP,PLL,Ga Perm ,,R2 (U R' U R' U' R U') R2 D (U' R' U R) D',y R U R' F' R U R' U' R' F R U' R' F R2 U' R' U' R U R' F',,,15,2,,,,https://speedcubedb.com/a/3x3/PLL,
63,3x3x3 Cube,WCA,NxNxN Cube,CFOP,PLL,Gb Perm,,(R' U' R U) D' R2 (U R' U R U' R U') R2 D,,,,15,2,,,,https://speedcubedb.com/a/3x3/PLL,
64,3x3x3 Cube,WCA,NxNxN Cube,CFOP,PLL,Gc Perm ,,R2 (U' R U' R U R' U) R2 D' (U R U' R') D,y2 R2 F2 R U2 R U2 R' F R U R' U' R' F R2,,,15,2,,,,https://speedcubedb.com/a/3x3/PLL,
65,3x3x3 Cube,WCA,NxNxN Cube,CFOP,PLL,Gd Perm,,(R U R' U') D R2 (U' R U' R' U R' U) R2 D',,,,15,2,,,,https://speedcubedb.com/a/3x3/PLL,
66,3x3x3 Cube,WCA,NxNxN Cube,CFOP,PLL,H Perm,,(M2 U' M2) U2 (M2 U' M2),,,,7,1,,,,https://speedcubedb.com/a/3x3/PLL,
67,3x3x3 Cube,WCA,NxNxN Cube,CFOP,PLL,Ja Perm,,y (R' U L') U2 (R U' R') U2 R L,y R' U L' U2 R U' R' U2 R L,,,11,2,,,,https://speedcubedb.com/a/3x3/PLL,
68,3x3x3 Cube,WCA,NxNxN Cube,CFOP,PLL,Jb Perm,,(R U R' F') (R U R' U') R' F R2 U' R',,,,13,2,,,,https://speedcubedb.com/a/3x3/PLL,
69,3x3x3 Cube,WCA,NxNxN Cube,CFOP,PLL,Na Perm,,(R U R' U) (R U R' F') (R U R' U') R' F R2 U' R' U2 (R U' R'),,,,21,3,,,,https://speedcubedb.com/a/3x3/PLL,
70,3x3x3 Cube,WCA,NxNxN Cube,CFOP,PLL,Nb Perm,,(R' U R U' R') (F' U' F) (R U R') (F R' F') (R U' R),r' D' F r U' r' F' D r2 U r' U' r' F r F',,,17,3,,,,https://speedcubedb.com/a/3x3/PLL,
71,3x3x3 Cube,WCA,NxNxN Cube,CFOP,PLL,Ra Perm,,y (R U' R' U') (R U R D) (R' U' R D') (R' U2 R'),,,,16,3,,,,https://speedcubedb.com/a/3x3/PLL,
72,3x3x3 Cube,WCA,NxNxN Cube,CFOP,PLL,Rb Perm,,(R' U2) (R U2) (R' F R) (U R' U' R') F' R2,y R2 F R U R U' R' F' R U2 R' U2 R,,,13,2,,,,https://speedcubedb.com/a/3x3/PLL,
73,3x3x3 Cube,WCA,NxNxN Cube,CFOP,PLL,T Perm,,(R U R' U') (R' F R2) (U' R' U') (R U R' F'),,,,14,2,,,,https://speedcubedb.com/a/3x3/PLL,
74,3x3x3 Cube,WCA,NxNxN Cube,CFOP,PLL,Ua Perm,,y2 (M2 U M) U2 (M' U M2),R U R' U R' U' R2 U' R' U R' U R,,,8,2,,,,https://speedcubedb.com/a/3x3/PLL,
75,3x3x3 Cube,WCA,NxNxN Cube,CFOP,PLL,Ub Perm,,y2 (M2 U' M) U2 (M' U' M2),R2' U R U R' U' R3 U' R' U R',,,8,2,,,,https://speedcubedb.com/a/3x3/PLL,
76,3x3x3 Cube,WCA,NxNxN Cube,CFOP,PLL,V Perm,,(R' U R' U') (R D' R' D) (R' U D') (R2 U' R2) D R2,R' U R U' x' U R U2 R' U' R U' R' U2 R U R' U',,,16,3,,,,https://speedcubedb.com/a/3x3/PLL,
77,3x3x3 Cube,WCA,NxNxN Cube,CFOP,PLL,Y Perm,,F R (U' R' U') (R U R' F') (R U R' U') (R' F R F'),,,,17,3,,,,https://speedcubedb.com/a/3x3/PLL,
78,3x3x3 Cube,WCA,NxNxN Cube,CFOP,PLL,Z Perm,,(M2 U) (M2 U) (M' U2) M2 (U2 M'),,,,9,2,,,,https://speedcubedb.com/a/3x3/PLL,
,,,,,,,,,,,,,,,,,,
79,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,1,Front-Right,U R U' R',,,,4,1,,,,,
80,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,1,Front-Left,F' r U r',,,,4,1,,,,,
81,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,1,Back-Left,U L U' L',,,,4,1,,,,,
82,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,1,Back-Right,U f R' f',,,,4,1,,,,,
83,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,2,Front-Right,F R' F' R,,,,4,1,,,,,
84,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,2,Front-Left,U' L' U L,,,,4,1,,,,,
85,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,2,Back-Left,l U L' U' M',,,,5,1,,,,,
86,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,2,Back-Right,U' R' U R,,,,4,1,,,,,
87,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,3,Front-Right,F' U' F,,,,3,1,,,,,
88,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,3,Front-Left,L' U' L,,,,3,1,,,,,
89,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,3,Back-Left,y R' U' R,,,,4,1,,,,,
90,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,3,Back-Right,R' U' R,,,,3,1,,,,,
91,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,4,Front-Right,,,,,,,,,,,
92,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,4,Front-Left,,,,,,,,,,,
93,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,4,Back-Left,,,,,,,,,,,
94,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,4,Back-Right,,,,,,,,,,,
95,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,5,Front-Right,,,,,,,,,,,
96,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,5,Front-Left,,,,,,,,,,,
97,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,5,Back-Left,,,,,,,,,,,
98,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,5,Back-Right,,,,,,,,,,,
99,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,6,Front-Right,,,,,,,,,,,
100,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,6,Front-Left,,,,,,,,,,,
101,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,6,Back-Left,,,,,,,,,,,
102,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,6,Back-Right,,,,,,,,,,,
103,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,7,Front-Right,,,,,,,,,,,
104,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,7,Front-Left,,,,,,,,,,,
105,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,7,Back-Left,,,,,,,,,,,
106,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,7,Back-Right,,,,,,,,,,,
107,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,8,Front-Right,,,,,,,,,,,
108,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,8,Front-Left,,,,,,,,,,,
109,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,8,Back-Left,,,,,,,,,,,
110,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,8,Back-Right,,,,,,,,,,,
111,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,9,Front-Right,,,,,,,,,,,
112,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,9,Front-Left,,,,,,,,,,,
113,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,9,Back-Left,,,,,,,,,,,
114,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,9,Back-Right,,,,,,,,,,,
115,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,10,Front-Right,,,,,,,,,,,
116,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,10,Front-Left,,,,,,,,,,,
117,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,10,Back-Left,,,,,,,,,,,
118,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,10,Back-Right,,,,,,,,,,,
119,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,11,Front-Right,,,,,,,,,,,
120,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,11,Front-Left,,,,,,,,,,,
121,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,11,Back-Left,,,,,,,,,,,
122,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,11,Back-Right,,,,,,,,,,,
123,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,12,Front-Right,,,,,,,,,,,
124,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,12,Front-Left,,,,,,,,,,,
125,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,12,Back-Left,,,,,,,,,,,
126,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,12,Back-Right,,,,,,,,,,,
127,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,13,Front-Right,,,,,,,,,,,
128,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,13,Front-Left,,,,,,,,,,,
129,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,13,Back-Left,,,,,,,,,,,
130,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,13,Back-Right,,,,,,,,,,,
131,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,14,Front-Right,,,,,,,,,,,
132,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,14,Front-Left,,,,,,,,,,,
133,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,14,Back-Left,,,,,,,,,,,
134,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,14,Back-Right,,,,,,,,,,,
135,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,15,Front-Right,,,,,,,,,,,
136,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,15,Front-Left,,,,,,,,,,,
137,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,15,Back-Left,,,,,,,,,,,
138,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,15,Back-Right,,,,,,,,,,,
139,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,16,Front-Right,,,,,,,,,,,
140,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,16,Front-Left,,,,,,,,,,,
141,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,16,Back-Left,,,,,,,,,,,
142,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,16,Back-Right,,,,,,,,,,,
143,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,17,Front-Right,,,,,,,,,,,
144,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,17,Front-Left,,,,,,,,,,,
145,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,17,Back-Left,,,,,,,,,,,
146,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,17,Back-Right,,,,,,,,,,,
147,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,18,Front-Right,,,,,,,,,,,
148,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,18,Front-Left,,,,,,,,,,,
149,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,18,Back-Left,,,,,,,,,,,
150,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,18,Back-Right,,,,,,,,,,,
151,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,19,Front-Right,,,,,,,,,,,
152,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,19,Front-Left,,,,,,,,,,,
153,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,19,Back-Left,,,,,,,,,,,
154,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,19,Back-Right,,,,,,,,,,,
155,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,20,Front-Right,,,,,,,,,,,
156,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,20,Front-Left,,,,,,,,,,,
157,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,20,Back-Left,,,,,,,,,,,
158,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,20,Back-Right,,,,,,,,,,,
159,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,21,Front-Right,,,,,,,,,,,
160,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,21,Front-Left,,,,,,,,,,,
161,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,21,Back-Left,,,,,,,,,,,
162,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,21,Back-Right,,,,,,,,,,,
163,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,22,Front-Right,,,,,,,,,,,
164,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,22,Front-Left,,,,,,,,,,,
165,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,22,Back-Left,,,,,,,,,,,
166,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,22,Back-Right,,,,,,,,,,,
167,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,23,Front-Right,,,,,,,,,,,
168,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,23,Front-Left,,,,,,,,,,,
169,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,23,Back-Left,,,,,,,,,,,
170,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,23,Back-Right,,,,,,,,,,,
171,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,24,Front-Right,,,,,,,,,,,
172,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,24,Front-Left,,,,,,,,,,,
173,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,24,Back-Left,,,,,,,,,,,
174,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,24,Back-Right,,,,,,,,,,,
175,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,25,Front-Right,,,,,,,,,,,
176,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,25,Front-Left,,,,,,,,,,,
177,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,25,Back-Left,,,,,,,,,,,
178,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,25,Back-Right,,,,,,,,,,,
179,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,26,Front-Right,,,,,,,,,,,
180,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,26,Front-Left,,,,,,,,,,,
181,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,26,Back-Left,,,,,,,,,,,
182,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,26,Back-Right,,,,,,,,,,,
183,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,27,Front-Right,,,,,,,,,,,
184,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,27,Front-Left,,,,,,,,,,,
185,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,27,Back-Left,,,,,,,,,,,
186,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,27,Back-Right,,,,,,,,,,,
187,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,28,Front-Right,,,,,,,,,,,
188,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,28,Front-Left,,,,,,,,,,,
189,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,28,Back-Left,,,,,,,,,,,
190,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,28,Back-Right,,,,,,,,,,,
191,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,29,Front-Right,,,,,,,,,,,
192,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,29,Front-Left,,,,,,,,,,,
193,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,29,Back-Left,,,,,,,,,,,
194,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,29,Back-Right,,,,,,,,,,,
195,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,30,Front-Right,,,,,,,,,,,
196,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,30,Front-Left,,,,,,,,,,,
197,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,30,Back-Left,,,,,,,,,,,
198,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,30,Back-Right,,,,,,,,,,,
199,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,31,Front-Right,,,,,,,,,,,
200,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,31,Front-Left,,,,,,,,,,,
201,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,31,Back-Left,,,,,,,,,,,
202,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,31,Back-Right,,,,,,,,,,,
203,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,32,Front-Right,,,,,,,,,,,
204,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,32,Front-Left,,,,,,,,,,,
205,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,32,Back-Left,,,,,,,,,,,
206,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,32,Back-Right,,,,,,,,,,,
207,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,33,Front-Right,,,,,,,,,,,
208,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,33,Front-Left,,,,,,,,,,,
209,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,33,Back-Left,,,,,,,,,,,
210,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,33,Back-Right,,,,,,,,,,,
211,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,34,Front-Right,,,,,,,,,,,
212,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,34,Front-Left,,,,,,,,,,,
213,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,34,Back-Left,,,,,,,,,,,
214,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,34,Back-Right,,,,,,,,,,,
215,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,35,Front-Right,,,,,,,,,,,
216,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,35,Front-Left,,,,,,,,,,,
217,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,35,Back-Left,,,,,,,,,,,
218,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,35,Back-Right,,,,,,,,,,,
219,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,36,Front-Right,,,,,,,,,,,
220,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,36,Front-Left,,,,,,,,,,,
221,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,36,Back-Left,,,,,,,,,,,
222,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,36,Back-Right,,,,,,,,,,,
223,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,37,Front-Right,,,,,,,,,,,
224,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,37,Front-Left,,,,,,,,,,,
225,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,37,Back-Left,,,,,,,,,,,
226,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,37,Back-Right,,,,,,,,,,,
227,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,38,Front-Right,,,,,,,,,,,
228,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,38,Front-Left,,,,,,,,,,,
229,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,38,Back-Left,,,,,,,,,,,
230,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,38,Back-Right,,,,,,,,,,,
231,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,39,Front-Right,,,,,,,,,,,
232,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,39,Front-Left,,,,,,,,,,,
233,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,39,Back-Left,,,,,,,,,,,
234,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,39,Back-Right,,,,,,,,,,,
235,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,40,Front-Right,,,,,,,,,,,
236,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,40,Front-Left,,,,,,,,,,,
237,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,40,Back-Left,,,,,,,,,,,
238,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,40,Back-Right,,,,,,,,,,,
239,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,41,Front-Right,,,,,,,,,,,
240,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,41,Front-Left,,,,,,,,,,,
241,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,41,Back-Left,,,,,,,,,,,
242,3x3x3 Cube,WCA,NxNxN Cube,CFOP,F2L,41,Back-Right,,,,,,,,,,,
// Turkish language file
export default {
  dir: "ltr",
  settings: {
    title: "<PERSON><PERSON><PERSON>",
    save: "<PERSON><PERSON>",
    close: "<PERSON><PERSON><PERSON>",
    language: "Dil",
  },
  info: {
    title: "<PERSON>il<PERSON>",
    pwaInstall: {
      title: "Uygulama Olarak Yükle",
      description:
        "En iyi deneyim için scTimer'ı Progressive Web App olarak yükleyin. Çevrimdışı çalışır ve yerel uygulama gibi hisseder.",
      install: "Uygulamayı Yükle",
      iosTitle: "iOS/iPad Kurulumu:",
      iosStep1: "1. Paylaş düğmesine dokunun",
      iosStep2: '2. A<PERSON>a<PERSON><PERSON> kaydırın ve "Ana Ekrana Ekle"ye dokunun',
      iosStep3: '3. Yüklemek için "Ekle"ye dokunun',
      note: "Chrome, Safari ve diğer modern tarayıcılarda mevcuttur",
    },
    shortcuts: {
      title: "<PERSON><PERSON><PERSON> Kısayolları",
      timer: "Zamanlayıcı Kontrolleri",
      spacebar: "Zamanlayıcıyı başlat/durdur",
      escape: "İncelemeyi iptal et ve modları kapat",
      navigation: "Navigasyon ve Eylemler",
      generate: "Yeni karıştırma oluştur",
      list: "Zaman listesini değiştir",
      settings: "Ayarları aç",
      edit: "Mevcut karıştırmayı düzenle",
      copy: "Karıştırmayı panoya kopyala",
      stats: "Detaylı istatistikleri aç",
      display: "Görünümü Değiştir",
      visualization: "Bulmaca görselleştirmesini değiştir",
      statistics: "İstatistik görünümünü değiştir",
      darkMode: "Karanlık modu değiştir",
      inspection: "WCA incelemesini değiştir",
      penalties: "Ceza Yönetimi",
      removePenalty: "Son çözümden cezayı kaldır",
      addPlus2: "Son çözüme +2 cezası ekle",
      addDNF: "Son çözüme DNF cezası ekle",
      session: "Oturum Yönetimi",
      emptySession: "Mevcut oturumu boşalt",
      exportSession: "Mevcut oturumu dışa aktar",
      eventSwitching: "Etkinlik Değiştirme",
      alt2to7: "2×2×2'den 7×7×7 küplerine geç",
      altP: "Pyraminx'e geç",
      altM: "Megaminx'e geç",
      altC: "Clock'a geç",
      altS: "Skewb'e geç",
      alt1: "Square-1'e geç",
      altF: "3×3×3 En Az Hamle'ye geç",
      altO: "3×3×3 Tek El'e geç",
      blindfolded: "Gözü Kapalı Etkinlikler",
      altCtrl3: "3×3×3 Gözü Kapalı'ya geç",
      altCtrl4: "4×4×4 Gözü Kapalı'ya geç",
      altCtrl5: "5×5×5 Gözü Kapalı'ya geç",
      altCtrl6: "3×3×3 Çoklu-Gözü Kapalı'ya geç",
      sessionMgmt: "Oturum Yönetimi",
      altN: "Yeni oturum oluştur",
    },
    gestures: {
      title: "Mobil Hareketler",
      swipeDown: "Aşağı Kaydır",
      swipeDownDesc: "Son çözümü sil",
      swipeUp: "Yukarı Kaydır",
      swipeUpDesc: "Cezaları değiştir (yok/+2/DNF)",
      swipeLeft: "Sola Kaydır",
      swipeLeftDesc: "LTR: Yeni karıştırma | RTL: Zaman listesi",
      swipeRight: "Sağa Kaydır",
      swipeRightDesc: "LTR: Zaman listesi | RTL: Yeni karıştırma",
      doubleClick: "Çift Tıklama",
      doubleClickDesc: "Mevcut karıştırmayı kopyala (PC/Mobil)",
      longPress: "Uzun Basma/Tıkla ve Tut",
      longPressDesc: "Mevcut karıştırmayı düzenle (PC/Mobil)",
    },
    features: {
      title: "Ana Özellikler",
      timer: "Profesyonel Zamanlayıcı",
      timerDesc: "İnceleme modu ile WCA uyumlu zamanlama",
      puzzles: "Tüm WCA Etkinlikleri",
      puzzlesDesc: "Tüm resmi WCA bulmaca etkinlikleri için tam destek",
      statistics: "Gelişmiş İstatistikler",
      statisticsDesc: "ao5, ao12, ao100 ile detaylı analizler",
      scrambles: "Resmi Karıştırmalar",
      scramblesDesc: "2D görselleştirme ile WCA standart karıştırma üretimi",
      multilingual: "Çok Dilli Destek",
      multilingualDesc: "RTL desteği ile 18+ dil",
      sync: "Google Drive Senkronizasyonu",
      syncDesc: "Akıllı birleştirme ile cihazlar arası senkronizasyon",
    },
    sync: {
      title: "Google Drive Senkronizasyonu",
      description:
        "Google Drive kullanarak çözüm sürelerinizi tüm cihazlarda senkronize edin. Verileriniz kişisel Google Drive hesabınızda güvenle saklanır.",
      secure: "Güvenli ve Özel",
      automatic: "Otomatik Senkronizasyon",
      offline: "Çevrimdışı Destek",
      smartMerge: "Akıllı Birleştirme",
      note: "Sürelerinizi tüm cihazlarınızda senkronize tutmak için Ayarlar'da Google Drive senkronizasyonunu etkinleştirin.",
    },
  },
  timerOptions: {
    title: "Zamanlayıcı Seçenekleri",
    warningSounds: "Uyarı Seslerini Etkinleştir",
    useInspection: "WCA İncelemesi Kullan (15s)",
    inspectionSound: "İnceleme Sesi:",
    inspectionSoundNone: "Yok",
    inspectionSoundVoice: "Ses",
    inspectionSoundBeep: "Bip",
    stackmatResetInspection: "Stackmat Sıfırlama İncelemeyi Etkinleştirir",
    stackmatResetNote: "Not: Sadece zamanlayıcı 0.000'da değilken çalışır",
    inputTimer: "Giriş Zamanlayıcı Modu (Süreleri manuel gir)",
    timerMode: "Zamanlayıcı Modu:",
    timerModeTimer: "Zamanlayıcı",
    timerModeTyping: "Yazma",
    timerModeStackmat: "Stackmat",
    timerModeBluetooth: "Bluetooth (Yakında)",
    microphoneInput: "Mikrofon Girişi",
    microphoneAuto: "Otomatik algılama",
    microphoneNote: "Y-splitter'ınızı veya harici mikrofonunuzu seçin",
    decimalPlaces: "Ondalık Basamaklar:",
    decimalPlacesNone: "Yok (12)",
    decimalPlaces1: "1 (12.3)",
    decimalPlaces2: "2 (12.34)",
    decimalPlaces3: "3 (12.345)",
  },
  displayOptions: {
    title: "Görünüm Seçenekleri",
    showVisualization: "Bulmaca Görselleştirmesini Göster",
    showStats: "İstatistikleri Göster",
    showDebug: "Hata Ayıklama Bilgilerini Göster",
    darkMode: "Karanlık Mod",
    showFMCKeyboard: "FMC Klavyesini Göster",
    scrambleFontSize: "Karıştırma Yazı Boyutu",
  },
  app: {
    title: "scTimer",
    description: "WCA incelemesi ve istatistiklerle speedcubing zamanlayıcısı",
    enterTime: "Süre gir",
    enterSolveTime: "Çözüm süresini manuel gir",
    generateScrambles: "Karıştırmalar Oluştur",
    outOf: "Toplam:",
    numberOfCubes: "Küp sayısı (minimum 2):",
    numberOfCubesSolved: "Çözülen küp sayısı:",
  },
  timer: {
    ready: "Hazır",
    running: "Çalışıyor",
    idle: "Boşta",
    inspection: "İnceleme",
    holding: "Tutuyor",
  },
  stats: {
    title: "İstatistikler",
    best: "En İyi",
    worst: "En Kötü",
    mean: "Ortalama",
    avg5: "ao5",
    avg12: "ao12",
    mean3: "mo3",
    bestMo3: "En İyi mo3",
    avg100: "ao100",
    avg1000: "ao1000",
    solves: "Çözümler",
    attempts: "Denemeler",
    moreStats: "Daha Fazla İstatistik",
  },
  statsDetails: {
    title: "İstatistik Detayları",
    titleFor: "İstatistik Detayları:",
    overview: "Genel Bakış",
    averages: "Ortalamalar",
    records: "Rekorlar",
    timeDistribution: "Süre Dağılımı",
    progressChart: "İlerleme Grafiği",
    sessionAnalysis: "Oturum Analizi",
    predictions: "Tahminler",
    standardDeviation: "Standart Sapma",
    bestSingle: "En İyi Tekil",
    bestAo5: "En İyi ao5",
    bestAo12: "En İyi ao12",
    bestAo100: "En İyi ao100",
    bestAo1000: "En İyi ao1000",
    totalTime: "Toplam Süre",
    averageTime: "Ortalama Süre",
    solvesPerHour: "Çözüm/Saat",
    consistency: "Tutarlılık",
    nextAo5: "Sonraki ao5 Hedefi",
    nextAo12: "Sonraki ao12 Hedefi",
    improvementRate: "İyileşme Oranı",
    targetTime: "Hedef Süre",
    currentSession: "Mevcut Oturum",
    allSessions: "Tüm Oturumlar",
    importTimes: "Süreleri İçe Aktar",
    exportJSON: "JSON Dışa Aktar",
    exportCSV: "CSV Dışa Aktar",
  },
  solveDetails: {
    title: "Çözüm Detayları",
    time: "Süre",
    date: "Tarih",
    scramble: "Karıştırma",
    editedScramble: "Düzenlenmiş Karıştırma",
    copyScramble: "Karıştırmayı kopyala",
    penalty: "Ceza",
    none: "Yok",
    comment: "Yorum",
    addComment: "Yorum ekle...",
    save: "Kaydet",
    share: "Paylaş",
    plusTwo: "+2",
    dnf: "DNF",
  },
  gestures: {
    scrambleCopied: "Karıştırma kopyalandı",
    noSolvesToDelete: "Silinecek çözüm yok",
    solveDeleted: "Çözüm silindi",
    cannotAddPenaltyMBLD: "MBLD çözümüne ceza eklenemez",
    dnfRemoved: "DNF kaldırıldı",
    dnfAdded: "DNF eklendi",
    plus2Added: "+2 cezası eklendi",
    penaltyRemoved: "Ceza kaldırıldı",
    newScrambleGenerated: "Yeni karıştırma oluşturuldu",
    timesPanelOpened: "Süreler paneli açıldı",
  },
  times: {
    title: "Çözüm Süreleri",
    clear: "Süreleri Temizle",
    close: "Kapat",
    delete: "Süreyi sil",
    confirmClear:
      "Bu etkinlik için tüm süreleri temizlemek istediğinizden emin misiniz?",
    confirmDelete: "Bu süreyi silmek istediğinizden emin misiniz?",
  },
  buttons: {
    viewTimes: "Süreleri Görüntüle",
    ok: "Tamam",
    cancel: "İptal",
  },
  events: {
    333: "3×3×3",
    222: "2×2×2",
    444: "4×4×4",
    555: "5×5×5",
    666: "6×6×6",
    777: "7×7×7",
    "333bf": "3×3×3 Gözü Kapalı",
    "333fm": "3×3×3 En Az Hamle",
    "333oh": "3×3×3 Tek El",
    clock: "Clock",
    minx: "Megaminx",
    pyram: "Pyraminx",
    skewb: "Skewb",
    sq1: "Square-1",
    "444bf": "4×4×4 Gözü Kapalı",
    "555bf": "5×5×5 Gözü Kapalı",
    "333mbf": "3×3×3 Çoklu-Gözü Kapalı",
  },
  mbld: {
    cubeCount: "Küpler",
    solvedCount: "Çözülen Küpler",
    totalCount: "Toplam Küp",
    totalCubes: "Toplam Küp",
    cubesSolved: "Çözülen Küpler",
    bestPoints: "En İyi Puan",
    successRate: "Başarı Oranı",
    points: "puan",
    save: "Sonucu Kaydet",
    visualizations: "Çoklu-Gözü Kapalı Görselleştirmeler",
    scrambles: "Çoklu-Gözü Kapalı Karıştırmalar",
    enterValidNumber: "Lütfen geçerli bir çözülen küp sayısı girin.",
    noScrambles:
      "MBLD karıştırmaları mevcut değil. Lütfen önce 3×3×3 Çoklu-Gözü Kapalı etkinliğini seçin.",
    visualizationNotFound:
      "Görselleştirme modalı bulunamadı. Lütfen sayfayı yenileyin ve tekrar deneyin.",
    containerNotFound:
      "Görselleştirme konteyneri bulunamadı. Lütfen sayfayı yenileyin ve tekrar deneyin.",
    clickToView:
      "Tüm küp görselleştirmelerini ve karıştırmaları görmek için tıklayın",
    bestScore: "En İyi Skor",
    worstScore: "En Kötü Skor",
    meanScore: "Ortalama Skor",
    averageScore: "Ortalama Skor",
    attempts: "deneme",
    totalAttempts: "Toplam Deneme",
    clickToViewScrambles: "Tüm karıştırmaları görmek için tıklayın",
    clickToViewScramblesCount: "Tüm {0} karıştırmayı görmek için tıklayın",
    setup: "Çoklu-Gözü Kapalı Kurulum",
    results: "Çoklu-Gözü Kapalı Sonuçlar",
    generateScrambles: "Karıştırmalar Oluştur",
    saveResult: "Sonucu Kaydet",
    cubeNumber: "Küp",
    numberOfCubesMinimum: "Küp sayısı (minimum 2):",
    numberOfCubesSolved: "Çözülen küp sayısı:",
    saveFirst: "Lütfen önce sonucunuzu kaydedin.",
    visualizationsTitle: "Çoklu-Gözü Kapalı Görselleştirmeler ({0} küp)",
    timeLimit: "Süre sınırı: {0} dakika",
    timeLimitExceeded: "Süre sınırı aşıldı. Sonuç DNF olacak.",
    negativePoints: "Negatif puan. Sonuç DNF olacak.",
  },
  modals: {
    error: "Hata",
    warning: "Uyarı",
    info: "Bilgi",
    confirm: "Onayla",
    prompt: "Giriş Gerekli",
  },
  stackmat: {
    error: "Stackmat Hatası",
    noMicrophone:
      "Stackmat zamanlayıcı başlatma başarısız: Mikrofon bulunamadı. Lütfen bir mikrofon bağlayın ve tekrar deneyin.",
    connected: "Bağlandı",
    disconnected: "Bağlantı Kesildi",
    settingUp: "Kuruluyor...",
  },
  sessions: {
    newSessionTitle: "Yeni Oturum",
    editSessionTitle: "Oturumu Düzenle",
    sessionName: "Oturum Adı:",
    sessionNamePlaceholder: "Benim Oturum",
    puzzleType: "Bulmaca Türü:",
    create: "Oluştur",
    save: "Kaydet",
  },
  scramble: {
    loading: "Karıştırma yükleniyor...",
  },
  debug: {
    timerState: "Zamanlayıcı Durumu: ",
    spaceHeldFor: "Boşluk Tuşu Basılı Tutma Süresi: ",
    currentEvent: "Mevcut Etkinlik: ",
    scrambleSource: "Karıştırma Kaynağı: ",
  },
  fmc: {
    title: "En Az Hamle Meydan Okuması",
    info: "Küpü mümkün olan en az hamle ile çözün. Çözüm bulmak için 60 dakikanız var.",
    timeRemaining: "Kalan Süre:",
    scramble: "Karıştırma:",
    solution: "Çözüm:",
    moveCount: "Hamle:",
    moves: "hamle",
    submit: "Gönder",
    resultTitle: "FMC Sonucu",
    resultTime: "Süre:",
    resultSolution: "Çözüm:",
    resultOk: "Tamam",
    solutionPlaceholder:
      "Çözümünüzü buraya standart WCA notasyonu kullanarak girin...",
    notationHelp: "Notasyon Yardımı:",
    notationHelpContent:
      "Yüz döndürmeleri: U, D, L, R, F, B (' veya 2 ekleriyle)<br>Geniş hamleler: Uw, Dw, vb.<br>Dilim hamleleri: M, E, S<br>Döndürmeler: x, y, z (toplam hamle sayısında sayılmaz)",
    submitSolution: "Çözümü Gönder",
    validSolution: "Geçerli çözüm",
    invalidNotation: "Geçersiz notasyon algılandı",
    bestMoves: "En İyi Hamle",
    worstMoves: "En Kötü Hamle",
    meanMoves: "Ortalama Hamle",
    bestMo3: "En İyi mo3",
    averageMoves: "Ortalama Hamle",
    attempts: "deneme",
    totalAttempts: "Toplam Deneme",
    tooManyMoves: "Çözüm 80 hamle sınırını aşıyor",
    timeExceeded:
      "Süre sınırı aşıldı. Gönderilmezse çözümünüz DNF olarak işaretlenecek.",
    confirmClose:
      "Kapatmak istediğinizden emin misiniz? Denemeniz DNF olarak işaretlenecek.",
    dnfReasonTimeout: "Süre sınırı aşıldı",
    dnfReasonInvalid: "Geçersiz notasyon",
    dnfReasonTooManyMoves: "Çözüm 80 hamleyi aşıyor",
    dnfReasonAbandoned: "Deneme terk edildi",
    confirmSubmit: "Çözümünüzü göndermek istediğinizden emin misiniz?",
    pressToStart: "FMC denemesini başlatmak için boşluk tuşuna basın",
    solutionAccepted: "Çözüm kabul edildi",
    clickToViewTwizzle:
      "Çözümü Twizzle'da görmek için aşağıdaki bağlantıya tıklayın",
    viewOnTwizzle: "Twizzle'da Görüntüle",
    moveCountLabel: "Hamle sayısı:",
    movesHTM: "hamle (HTM)",
    timeUsedLabel: "Kullanılan süre:",
    loadingFMC: "FMC yükleniyor",
    generatingScramble: "karıştırma oluşturuluyor ve arayüz hazırlanıyor",
  },
  tutorial: {
    welcomeTitle: "scTimer'a Hoş Geldiniz!",
    welcomeSubtitle: "Profesyonel speedcubing zamanlayıcınız",
    selectLanguage: "Dil Seçin:",
    feature1: "WCA Standart Zamanlayıcı",
    feature2: "Gelişmiş İstatistikler",
    feature3: "Tüm WCA Etkinlikleri",
    feature4: "Karıştırma Üretici",
    welcomeDescription:
      "scTimer'ı etkili bir şekilde nasıl kullanacağınızı öğrenmek için hızlı bir tur ister misiniz? Öğretici sizi sadece birkaç adımda ana özellikler boyunca yönlendirecek.",
    skipTutorial: "Öğreticiyi Atla",
    startTour: "Turu Başlat",
    step1: {
      title: "Karıştırma Görünümü",
      text: "Bu, mevcut bulmacanız için karıştırma dizisini gösterir. Her karıştırma WCA standartlarına göre rastgele oluşturulur.",
    },
    step2: {
      title: "Zamanlayıcı Kontrolleri",
      text: "Zamanlamayı başlatmak için BOŞLUK tuşunu basılı tutun, çözmeye başlamak için bırakın. Mobilde zamanlayıcı alanına dokunun ve tutun. Zamanlayıcı WCA inceleme standartlarını takip eder.",
    },
    step3: {
      title: "Etkinlik Seçici",
      text: "3x3x3, 2x2x2, 4x4x4 ve diğer birçok bulmaca türü dahil tüm WCA etkinlikleri arasından seçin. Açılır menüyü açmak için tıklayın veya dokunun.",
    },
    step4: {
      title: "İstatistik Takibi",
      text: "En iyi süre, 5, 12 ve 100 çözümün ortalamaları dahil detaylı istatistiklerle ilerlemenizi takip edin. Daha fazla ayrıntı görmek için herhangi bir istatistiğe tıklayın.",
    },
    step5: {
      title: "Yeni Karıştırma Oluşturma",
      text: "Bir sonraki çözüm için hazır olduğunuzda yeni bir karıştırma oluşturun. Klavye kısayolu: N tuşuna basın veya karıştırma simgesine tıklayın.",
    },
    step6: {
      title: "Ayarlar ve Özelleştirme",
      text: "İnceleme süresi, ses seçenekleri, zamanlayıcı modları ve görünüm tercihleri ile zamanlayıcı deneyiminizi özelleştirin. Klavye kısayolu: S tuşuna basın.",
    },
    step7: {
      title: "Klavye Kısayolları",
      text: "Bu kısayolları öğrenin: BOŞLUK (zamanlayıcıyı başlat/durdur), N (yeni karıştırma), S (ayarlar), ESC (modları kapat), Oklar (gezin). Mobilde kaydırma hareketlerini kullanın!",
    },
    step8: {
      title: "Mobil Hareketler",
      text: "Mobil cihazlarda: Süreler panelini açmak için sola kaydırın, kapatmak için sağa kaydırın, başlatmak için zamanlayıcıya dokunun ve tutun, kopyalamak için karıştırmaya çift dokunun. Görselleştirmeleri yakınlaştırmak için çimdikleyin.",
    },
    step9: {
      title: "Profesyonel İpuçları ve Özellikler",
      text: "WCA pratiği için ayarlarda inceleme süresini etkinleştirin. Farklı etkinlikleri takip etmek için farklı oturumlar kullanın. Analiz için sürelerinizi dışa aktarın. Zamanlayıcı PWA olarak çevrimdışı çalışır!",
    },
    previous: "Önceki",
    next: "Sonraki",
    finish: "Bitir",
    close: "Kapat",
    stepCounter: "/",
    restartTutorial: "Öğreticiyi Yeniden Başlat",
  },
};

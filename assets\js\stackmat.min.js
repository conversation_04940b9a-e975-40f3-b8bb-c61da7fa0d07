!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):(t="undefined"!=typeof globalThis?globalThis:t||self).Stackmat=e()}(this,(function(){"use strict";var t;function e(t){return-1!=="IA SLRC".indexOf(String.fromCharCode(t))}function s(t){return-1!=="0123456789".indexOf(String.fromCharCode(t))}function i(t,e){return t===e.map((t=>Number(String.fromCharCode(t)))).reduce(((t,e)=>t+e))+64}!function(t){t.IDLE="I",t.STARTING="A",t.RUNNING=" ",t.STOPPED="S",t.LEFT_HAND="L",t.RIGHT_HAND="R",t.BOTH_HANDS="C",t.INVALID="X"}(t||(t={}));class n{constructor(e,s,i,n){this.isValid=e,this.status=s||t.INVALID,this.stringDigits=i||[],this.timeInMilliseconds=n||0}get timeAsString(){return this.stringDigits.slice(0,1)+":"+this.stringDigits.slice(1,3).join("")+"."+this.stringDigits.slice(3).join("")}get isLeftHandDown(){return this.status===t.LEFT_HAND||this.status===t.BOTH_HANDS||this.status===t.STARTING}get isRightHandDown(){return this.status===t.RIGHT_HAND||this.status===t.BOTH_HANDS||this.status===t.STARTING}get areBothHandsDown(){return this.status===t.BOTH_HANDS||this.status===t.STARTING}}class o extends n{constructor(){super(!1)}}class a extends n{static isValid(t){return 9===t.length&&e(t[0])&&s(t[1])&&s(t[2])&&s(t[3])&&s(t[4])&&s(t[5])&&i(t[6],t.slice(1,6))&&10===t[7]&&13===t[8]}constructor(t){if(a.isValid(t)){const e=String.fromCharCode(t[0]),s=t.slice(1,6).map((t=>String.fromCharCode(t))),i=Number(s[0]),n=Number(s.slice(1,3).join("")),o=Number(s.slice(3).join("")+"0");super(!0,e,s,6e4*i+1e3*n+o)}else super(!1)}}class r extends n{static isValid(t){return 10===t.length&&e(t[0])&&s(t[1])&&s(t[2])&&s(t[3])&&s(t[4])&&s(t[5])&&s(t[6])&&i(t[7],t.slice(1,7))&&10===t[8]&&13===t[9]}constructor(t){if(r.isValid(t)){const e=String.fromCharCode(t[0]),s=t.slice(1,7).map((t=>String.fromCharCode(t))),i=Number(s[0]),n=Number(s.slice(1,3).join("")),o=Number(s.slice(3).join(""));super(!0,e,s,6e4*i+1e3*n+o)}else super(!1)}}class c{constructor(t,e){this.ticksPerBit=t/1200,this.callback=e}decode(t){let e=[];for(const s of t)e.push(s<=0?1:0);const s=this.findBeginningOfSignal(e),i=function(t){let e=-1;const s=[];for(const i of t)e!==i?(s.push({bit:i,length:1}),e=i):s[s.length-1].length++;return s}(e.slice(s));e=this.getBitsFromRunLengthEncodedSignal(i);const n=function(t){const e=[];for(let s=0;s<=9;s++)e.push(h(t,10*s));let s=new r(e);return s.isValid||(s=new a(e.slice(0,-1))),s}(e.slice(1));n.isValid&&this.callback(n)}findBeginningOfSignal(t){let e=0,s=!1;for(let i=0;i<t.length;i++)if(1===t[i])e++,e>9*this.ticksPerBit&&(s=!0);else if(e=0,s)return i}getBitsFromRunLengthEncodedSignal(t){const e=t.map((t=>Array(Math.round(t.length/this.ticksPerBit)).fill(t.bit)));return[].concat(...e)}}function h(t,e){let s=0;for(let i=0;i<8;i++)s+=t[e+i]<<i;return s}const l=URL.createObjectURL(new Blob(["(",function(){class t extends AudioWorkletProcessor{constructor(){super(),this.bufferSize=10240,this.chunkBuffer=new Float32Array(this.bufferSize),this.offset=0}process(t){const e=t[0][0];return this.chunkBuffer.set(e,this.offset),this.offset+=e.length,this.offset>=this.bufferSize&&(this.port.postMessage(this.chunkBuffer),this.chunkBuffer=new Float32Array(this.bufferSize),this.offset=0),!0}}registerProcessor("stackmat-processor",t)}.toString(),")()"],{type:"application/javascript"}));class d{constructor(t){this.callback=t}start(){navigator.mediaDevices&&navigator.mediaDevices.getUserMedia&&navigator.mediaDevices.getUserMedia({audio:{echoCancellation:!1,noiseSuppression:!1}}).then((t=>{this.stream=t,this.context=new AudioContext,this.source=this.context.createMediaStreamSource(this.stream);const e=new c(this.context.sampleRate,this.callback);this.context.audioWorklet.addModule(l).then((()=>{this.context&&this.source&&(this.workletNode=new AudioWorkletNode(this.context,"stackmat-processor",{numberOfInputs:1,numberOfOutputs:1}),this.workletNode.port.onmessage=t=>{e.decode(t.data)},this.source.connect(this.workletNode),this.workletNode.connect(this.source.context.destination))}))})).catch((t=>{console.error(t)}))}stop(){this.context&&this.source&&this.workletNode&&(this.source.disconnect(this.workletNode),this.workletNode.disconnect(this.context.destination),this.workletNode=void 0,this.source=void 0,this.context.close().finally((()=>{this.context=void 0}))),this.stream&&(this.stream.getTracks().forEach((t=>t.stop())),this.stream=void 0)}}const u=["packetReceived","timerConnected","timerDisconnected","started","stopped","reset","ready","unready","starting","leftHandDown","leftHandUp","rightHandDown","rightHandUp"],f=t=>"string"==typeof t&&u.includes(t);class g{constructor(){this.lastPacketRunning=!1,this.lastPacketReset=!1,this.handlers=new Map,this.connected=!1,this.lastPacketReceived=0,this.connectionTimout=1e3,setInterval((()=>{this.connected&&this.lastPacketReceived+this.connectionTimout<Date.now()?(this.connected=!1,this.fire("timerDisconnected",this.lastPacket||new o)):!this.connected&&this.lastPacketReceived+this.connectionTimout>Date.now()&&(this.connected=!0,this.fire("timerConnected",this.lastPacket||new o))}),100,this)}on(t,e){if(f(t)){const s=this.handlers.get(t)||[];s.push(e),this.handlers.set(t,s)}}off(t){t&&f(t)&&this.handlers.has(t)?this.handlers.delete(t):t||this.handlers.clear()}receivePacket(e){this.lastPacketReceived=Date.now(),this.fire("packetReceived",e),this.lastPacket&&(this.lastPacket.isLeftHandDown&&!e.isLeftHandDown?this.fire("leftHandUp",e):!this.lastPacket.isLeftHandDown&&e.isLeftHandDown&&this.fire("leftHandDown",e),this.lastPacket.isRightHandDown&&!e.isRightHandDown?this.fire("rightHandUp",e):!this.lastPacket.isRightHandDown&&e.isRightHandDown&&this.fire("rightHandDown",e),this.lastPacket.areBothHandsDown||e.status!==t.BOTH_HANDS||0!==this.lastPacket.timeInMilliseconds||this.fire("ready",e),this.lastPacket.status!==t.BOTH_HANDS||e.areBothHandsDown||e.status===t.RUNNING||0!==e.timeInMilliseconds||this.fire("unready",e),this.lastPacket.status===t.BOTH_HANDS&&e.status===t.STARTING&&this.fire("starting",e),!this.lastPacketRunning&&(e.status===t.RUNNING||0===this.lastPacket.timeInMilliseconds&&e.timeInMilliseconds>0)&&(this.lastPacketRunning=!0,this.lastPacketReset=!1,this.fire("started",e)),this.lastPacketRunning&&(e.status===t.STOPPED||e.status===t.BOTH_HANDS||this.lastPacket.timeInMilliseconds===e.timeInMilliseconds&&e.timeInMilliseconds>0)&&(this.lastPacketRunning=!1,this.lastPacketReset=!1,this.lastPacket.isLeftHandDown||e.isLeftHandDown||this.fire("leftHandDown",e),this.lastPacket.isRightHandDown||e.isRightHandDown||this.fire("rightHandDown",e),this.fire("stopped",e),this.lastPacket.isLeftHandDown||e.isLeftHandDown||this.fire("leftHandUp",e),this.lastPacket.isRightHandDown||e.isRightHandDown||this.fire("rightHandUp",e)),this.lastPacketReset||e.status!==t.IDLE||(this.lastPacketRunning=!1,this.lastPacketReset=!0,this.fire("reset",e))),this.lastPacket=e}fire(t,e){for(const s of this.handlers.get(t)||[])s(e)}}return class{constructor(){this.eventManager=new g}on(t,e){return!!f(t)&&(this.eventManager.on(t,e),!0)}off(t){return!(t&&!f(t))&&(this.eventManager.off(t),!0)}start(){this.audioProcessor&&this.stop(),this.audioProcessor=new d((t=>this.eventManager.receivePacket(t))),this.audioProcessor.start()}stop(){this.audioProcessor&&(this.audioProcessor.stop(),this.audioProcessor=void 0)}}}));

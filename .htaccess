# Enable URL Rewriting
RewriteEngine On

# Redirect all requests to the sctimer folder
# This makes sctimer.com show content from sctimer.com/sctimer/ 
# without changing the URL in the browser

# Don't rewrite if the file or directory exists in the root
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d

# Don't rewrite if already accessing the sctimer folder directly
RewriteCond %{REQUEST_URI} !^/sctimer/

# Rewrite all other requests to the sctimer folder
RewriteRule ^(.*)$ /sctimer/$1 [L]

# Optional: Prevent direct access to the sctimer folder
# Uncomment the lines below if you want to block direct access to sctimer.com/sctimer/
# RewriteCond %{THE_REQUEST} ^[A-Z]{3,}\s/+sctimer/ [NC]
# RewriteRule ^sctimer/(.*)$ /$1 [R=301,L]

/**
 * Lottie Loader Utility - Provides reusable Lottie animation loaders
 * Uses the loaderCube.json animation for all loading states
 */

// Cache for loaded animation data
let animationData = null;
let isLoading = false;

/**
 * Load the Lottie animation data
 * @returns {Promise<Object>} The animation data
 */
async function loadAnimationData() {
  if (animationData) {
    return animationData;
  }

  if (isLoading) {
    // Wait for the current loading to complete
    return new Promise((resolve) => {
      const checkLoaded = () => {
        if (animationData) {
          resolve(animationData);
        } else {
          setTimeout(checkLoaded, 50);
        }
      };
      checkLoaded();
    });
  }

  isLoading = true;

  try {
    const response = await fetch('assets/loaderCube.json');
    if (!response.ok) {
      throw new Error(`Failed to load animation: ${response.status}`);
    }
    animationData = await response.json();
    isLoading = false;
    return animationData;
  } catch (error) {
    isLoading = false;
    console.error('Failed to load Lottie animation:', error);
    throw error;
  }
}

/**
 * Create a Lottie loader element
 * @param {Object} options - Configuration options
 * @param {string} options.containerId - ID of the container element
 * @param {number} options.width - Width of the animation (default: 40)
 * @param {number} options.height - Height of the animation (default: 40)
 * @param {boolean} options.loop - Whether to loop the animation (default: true)
 * @param {boolean} options.autoplay - Whether to autoplay the animation (default: true)
 * @param {string} options.className - Additional CSS class for the container
 * @returns {Promise<Object>} The Lottie animation instance
 */
async function createLottieLoader(options = {}) {
  const {
    containerId,
    width = 40,
    height = 40,
    loop = true,
    autoplay = true,
    className = ''
  } = options;

  if (!containerId) {
    throw new Error('containerId is required');
  }

  const container = document.getElementById(containerId);
  if (!container) {
    throw new Error(`Container with ID '${containerId}' not found`);
  }

  try {
    const data = await loadAnimationData();
    
    // Clear any existing content
    container.innerHTML = '';
    
    // Add CSS class if provided
    if (className) {
      container.classList.add(className);
    }

    // Set container size
    container.style.width = `${width}px`;
    container.style.height = `${height}px`;
    container.style.display = 'inline-block';

    // Create Lottie animation
    const animation = lottie.loadAnimation({
      container: container,
      renderer: 'svg',
      loop: loop,
      autoplay: autoplay,
      animationData: data
    });

    return animation;
  } catch (error) {
    console.error('Failed to create Lottie loader:', error);
    // Fallback to a simple loading text
    container.innerHTML = '<div style="display: flex; align-items: center; justify-content: center; width: 100%; height: 100%; font-size: 12px; color: var(--text-color);">Loading...</div>';
    throw error;
  }
}

/**
 * Create a Lottie loader and insert it into an element
 * @param {HTMLElement} element - The element to insert the loader into
 * @param {Object} options - Configuration options
 * @param {number} options.width - Width of the animation (default: 40)
 * @param {number} options.height - Height of the animation (default: 40)
 * @param {boolean} options.loop - Whether to loop the animation (default: true)
 * @param {boolean} options.autoplay - Whether to autoplay the animation (default: true)
 * @param {string} options.className - Additional CSS class for the container
 * @returns {Promise<Object>} The Lottie animation instance
 */
async function insertLottieLoader(element, options = {}) {
  const {
    width = 40,
    height = 40,
    loop = true,
    autoplay = true,
    className = ''
  } = options;

  if (!element) {
    throw new Error('Element is required');
  }

  try {
    const data = await loadAnimationData();
    
    // Clear any existing content
    element.innerHTML = '';
    
    // Add CSS class if provided
    if (className) {
      element.classList.add(className);
    }

    // Set element size and display
    element.style.width = `${width}px`;
    element.style.height = `${height}px`;
    element.style.display = 'inline-block';

    // Create Lottie animation
    const animation = lottie.loadAnimation({
      container: element,
      renderer: 'svg',
      loop: loop,
      autoplay: autoplay,
      animationData: data
    });

    return animation;
  } catch (error) {
    console.error('Failed to insert Lottie loader:', error);
    // Fallback to a simple loading text
    element.innerHTML = '<div style="display: flex; align-items: center; justify-content: center; width: 100%; height: 100%; font-size: 12px; color: var(--text-color);">Loading...</div>';
    throw error;
  }
}

/**
 * Create a Lottie loader HTML string
 * @param {Object} options - Configuration options
 * @param {string} options.id - ID for the container element
 * @param {number} options.width - Width of the animation (default: 40)
 * @param {number} options.height - Height of the animation (default: 40)
 * @param {string} options.className - Additional CSS class for the container
 * @returns {string} HTML string for the loader container
 */
function createLottieLoaderHTML(options = {}) {
  const {
    id = `lottie-loader-${Date.now()}`,
    width = 40,
    height = 40,
    className = ''
  } = options;

  return `<div id="${id}" class="lottie-loader ${className}" style="width: ${width}px; height: ${height}px; display: inline-block;"></div>`;
}

/**
 * Initialize a Lottie loader from an existing HTML element
 * @param {string} containerId - ID of the container element
 * @param {Object} options - Configuration options
 * @returns {Promise<Object>} The Lottie animation instance
 */
async function initLottieLoader(containerId, options = {}) {
  return createLottieLoader({
    containerId,
    ...options
  });
}

// Export functions for use in other modules
export {
  createLottieLoader,
  insertLottieLoader,
  createLottieLoaderHTML,
  initLottieLoader,
  loadAnimationData
};

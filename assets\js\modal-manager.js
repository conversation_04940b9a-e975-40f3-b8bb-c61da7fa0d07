// Modal Manager - Handles custom modals for the application

// Create a modal with the given content and options
export function showModal(options) {
  // Default options
  const defaults = {
    title: "",
    message: "",
    confirmText: "OK",
    cancelText: "Cancel",
    showCancel: false,
    onConfirm: null,
    onCancel: null,
    inputType: null, // 'text', 'number', etc.
    inputValue: "",
    inputPlaceholder: "",
    inputMin: null,
    inputMax: null,
    inputStep: null,
    className: "",
  };

  // Merge options with defaults
  const settings = { ...defaults, ...options };

  // Create modal container if it doesn't exist
  let modalContainer = document.getElementById("custom-modal-container");
  if (!modalContainer) {
    modalContainer = document.createElement("div");
    modalContainer.id = "custom-modal-container";
    document.body.appendChild(modalContainer);
  }

  // Create modal element
  const modal = document.createElement("div");
  modal.className = `custom-modal ${settings.className}`;

  // Create modal content
  const modalContent = document.createElement("div");
  modalContent.className = "custom-modal-content";

  // Add title if provided
  if (settings.title) {
    const title = document.createElement("h3");
    title.className = "custom-modal-title";
    title.textContent = settings.title;
    modalContent.appendChild(title);
  }

  // Add message if provided
  if (settings.message) {
    const message = document.createElement("div");
    message.className = "custom-modal-message";
    message.textContent = settings.message;
    modalContent.appendChild(message);
  }

  // Add input if requested
  let inputElement = null;
  if (settings.inputType) {
    const inputContainer = document.createElement("div");
    inputContainer.className = "custom-modal-input-container";

    inputElement = document.createElement("input");
    inputElement.type = settings.inputType;
    inputElement.className = "custom-modal-input";
    inputElement.value = settings.inputValue;
    inputElement.placeholder = settings.inputPlaceholder;

    if (settings.inputMin !== null) inputElement.min = settings.inputMin;
    if (settings.inputMax !== null) inputElement.max = settings.inputMax;
    if (settings.inputStep !== null) inputElement.step = settings.inputStep;

    inputContainer.appendChild(inputElement);
    modalContent.appendChild(inputContainer);
  }

  // Create buttons container
  const buttonsContainer = document.createElement("div");
  buttonsContainer.className = "custom-modal-buttons";

  // Add confirm button
  const confirmButton = document.createElement("button");
  confirmButton.className = "custom-modal-button custom-modal-confirm";
  confirmButton.textContent = settings.confirmText;
  buttonsContainer.appendChild(confirmButton);

  // Add cancel button if needed
  let cancelButton = null;
  if (settings.showCancel) {
    cancelButton = document.createElement("button");
    cancelButton.className = "custom-modal-button custom-modal-cancel";
    cancelButton.textContent = settings.cancelText;
    buttonsContainer.appendChild(cancelButton);
  }

  // Add buttons to modal
  modalContent.appendChild(buttonsContainer);
  modal.appendChild(modalContent);

  // Add modal to container
  modalContainer.innerHTML = "";
  modalContainer.appendChild(modal);

  // Show the modal
  setTimeout(() => {
    modal.classList.add("show");

    // Focus the input if present, otherwise focus the confirm button
    if (inputElement) {
      inputElement.focus();
    } else {
      confirmButton.focus();
    }
  }, 10);

  // Handle confirm button click
  confirmButton.addEventListener("click", () => {
    modal.classList.remove("show");

    // Call onConfirm callback with input value if provided
    if (settings.onConfirm) {
      if (inputElement) {
        settings.onConfirm(inputElement.value);
      } else {
        settings.onConfirm();
      }
    }

    // Remove modal after animation
    setTimeout(() => {
      modalContainer.innerHTML = "";
    }, 300);
  });

  // Handle cancel button click if shown
  if (settings.showCancel) {
    cancelButton.addEventListener("click", () => {
      modal.classList.remove("show");

      // Call onCancel callback if provided
      if (settings.onCancel) {
        settings.onCancel();
      }

      // Remove modal after animation
      setTimeout(() => {
        modalContainer.innerHTML = "";
      }, 300);
    });
  }

  // Handle click outside to close (if cancel is enabled)
  if (settings.showCancel) {
    modal.addEventListener("click", (e) => {
      if (e.target === modal) {
        modal.classList.remove("show");

        // Call onCancel callback if provided
        if (settings.onCancel) {
          settings.onCancel();
        }

        // Remove modal after animation
        setTimeout(() => {
          modalContainer.innerHTML = "";
        }, 300);
      }
    });
  }

  // Handle escape key to close (if cancel is enabled)
  if (settings.showCancel) {
    const handleEscape = (e) => {
      if (e.key === "Escape") {
        modal.classList.remove("show");

        // Call onCancel callback if provided
        if (settings.onCancel) {
          settings.onCancel();
        }

        // Remove modal after animation
        setTimeout(() => {
          modalContainer.innerHTML = "";
        }, 300);

        // Remove event listener
        document.removeEventListener("keydown", handleEscape);
      }
    };

    document.addEventListener("keydown", handleEscape);
  }
}

// Shorthand for alert modal
export function showAlert(message, title = "", onClose = null) {
  // Get translations if available
  const translations = window.i18nModule?.translations || {};
  const buttonTranslations = translations.buttons || {};

  showModal({
    title: title,
    message: message,
    confirmText: buttonTranslations.ok || "OK",
    onConfirm: onClose,
  });
}

// Shorthand for confirm modal
export function showConfirm(message, onConfirm, onCancel = null, title = "") {
  // Get translations if available
  const translations = window.i18nModule?.translations || {};
  const buttonTranslations = translations.buttons || {};

  showModal({
    title: title,
    message: message,
    confirmText: buttonTranslations.ok || "OK",
    cancelText: buttonTranslations.cancel || "Cancel",
    showCancel: true,
    onConfirm: onConfirm,
    onCancel: onCancel,
  });
}

// Shorthand for prompt modal
export function showPrompt(
  message,
  onConfirm,
  defaultValue = "",
  title = "",
  inputType = "text"
) {
  // Get translations if available
  const translations = window.i18nModule?.translations || {};
  const buttonTranslations = translations.buttons || {};

  showModal({
    title: title,
    message: message,
    confirmText: buttonTranslations.ok || "OK",
    cancelText: buttonTranslations.cancel || "Cancel",
    showCancel: true,
    inputType: inputType,
    inputValue: defaultValue,
    onConfirm: onConfirm,
  });
}

// Polish language file
export default {
  dir: "ltr",
  settings: {
    title: "Ustawi<PERSON>",
    save: "<PERSON><PERSON><PERSON><PERSON>",
    close: "<PERSON>amk<PERSON><PERSON>",
    language: "Języ<PERSON>",
  },
  info: {
    title: "Informacje",
    pwaInstall: {
      title: "Zainstaluj jako <PERSON>",
      description:
        "Zainstaluj scTimer jako Progressive Web App dla najlepszego doświadczenia. Działa offline i czuje się jak natywna aplikacja.",
      install: "Zainstaluj Aplikację",
      iosTitle: "Instalacja iOS/iPad:",
      iosStep1: "1. Dotknij przycisk Udostępnij",
      iosStep2: '2. Przewiń w dół i dotknij "Dodaj do ekranu głównego"',
      iosStep3: '3. Dotknij "Dodaj" aby zainstalować',
      note: "Dostępne w Chrome, Safari i innych nowoczesnych przeglądarkach",
    },
    shortcuts: {
      title: "<PERSON><PERSON><PERSON><PERSON><PERSON>",
      timer: "Sterowanie Timerem",
      spacebar: "Start/stop timer",
      escape: "Anuluj inspekcję i zamknij modale",
      navigation: "Nawigacja i Akcje",
      generate: "Generuj nowy scramble",
      list: "Przełącz listę czasów",
      settings: "Otwórz ustawienia",
      edit: "Edytuj bieżący scramble",
      copy: "Kopiuj scramble do schowka",
      stats: "Otwórz szczegółowe statystyki",
      display: "Przełącz Wyświetlanie",
      visualization: "Przełącz wizualizację układanki",
      statistics: "Przełącz wyświetlanie statystyk",
      darkMode: "Przełącz tryb ciemny",
      inspection: "Przełącz inspekcję WCA",
      penalties: "Zarządzanie Karami",
      removePenalty: "Usuń karę z ostatniego ułożenia",
      addPlus2: "Dodaj karę +2 do ostatniego ułożenia",
      addDNF: "Dodaj karę DNF do ostatniego ułożenia",
      session: "Zarządzanie Sesjami",
      emptySession: "Opróżnij bieżącą sesję",
      exportSession: "Eksportuj bieżącą sesję",
      eventSwitching: "Przełączanie Wydarzeń",
      alt2to7: "Przełącz na kostki 2×2×2 do 7×7×7",
      altP: "Przełącz na Pyraminx",
      altM: "Przełącz na Megaminx",
      altC: "Przełącz na Clock",
      altS: "Przełącz na Skewb",
      alt1: "Przełącz na Square-1",
      altF: "Przełącz na 3×3×3 Najmniej Ruchów",
      altO: "Przełącz na 3×3×3 Jedną Ręką",
      blindfolded: "Wydarzenia Na Ślepo",
      altCtrl3: "Przełącz na 3×3×3 Na Ślepo",
      altCtrl4: "Przełącz na 4×4×4 Na Ślepo",
      altCtrl5: "Przełącz na 5×5×5 Na Ślepo",
      altCtrl6: "Przełącz na 3×3×3 Multi-Na Ślepo",
      sessionMgmt: "Zarządzanie Sesjami",
      altN: "Utwórz nową sesję",
    },
    gestures: {
      title: "Gesty Mobilne",
      swipeDown: "Przesuń w Dół",
      swipeDownDesc: "Usuń ostatnie ułożenie",
      swipeUp: "Przesuń w Górę",
      swipeUpDesc: "Przełączaj kary (brak/+2/DNF)",
      swipeLeft: "Przesuń w Lewo",
      swipeLeftDesc: "LTR: Nowy scramble | RTL: Lista czasów",
      swipeRight: "Przesuń w Prawo",
      swipeRightDesc: "LTR: Lista czasów | RTL: Nowy scramble",
      doubleClick: "Podwójne Kliknięcie",
      doubleClickDesc: "Kopiuj bieżący scramble (PC/Mobilny)",
      longPress: "Długie Naciśnięcie/Kliknij i Przytrzymaj",
      longPressDesc: "Edytuj bieżący scramble (PC/Mobilny)",
    },
    features: {
      title: "Główne Funkcje",
      timer: "Profesjonalny Timer",
      timerDesc: "Pomiar czasu zgodny z WCA z trybem inspekcji",
      puzzles: "Wszystkie Wydarzenia WCA",
      puzzlesDesc:
        "Pełne wsparcie dla wszystkich oficjalnych wydarzeń układanek WCA",
      statistics: "Zaawansowane Statystyki",
      statisticsDesc: "Szczegółowe analizy z ao5, ao12, ao100",
      scrambles: "Oficjalne Scramble",
      scramblesDesc: "Generowanie scramble standardu WCA z wizualizacją 2D",
      multilingual: "Wsparcie Wielojęzyczne",
      multilingualDesc: "15+ języków ze wsparciem RTL",
      sync: "Synchronizacja Google Drive",
      syncDesc: "Synchronizacja między urządzeniami z inteligentnym łączeniem",
    },
    sync: {
      title: "Synchronizacja Google Drive",
      description:
        "Synchronizuj swoje czasy ułożeń na wszystkich urządzeniach używając Google Drive. Twoje dane są bezpiecznie przechowywane na Twoim osobistym koncie Google Drive.",
      secure: "Bezpieczne i Prywatne",
      automatic: "Automatyczna Synchronizacja",
      offline: "Wsparcie Offline",
      smartMerge: "Inteligentne Łączenie",
      note: "Włącz synchronizację Google Drive w Ustawieniach aby utrzymać swoje czasy zsynchronizowane na wszystkich urządzeniach.",
    },
  },
  timerOptions: {
    title: "Opcje Timera",
    warningSounds: "Włącz Dźwięki Ostrzegawcze",
    useInspection: "Użyj Inspekcji WCA (15s)",
    inspectionSound: "Dźwięk Inspekcji:",
    inspectionSoundNone: "Brak",
    inspectionSoundVoice: "Głos",
    inspectionSoundBeep: "Sygnał",
    stackmatResetInspection: "Reset Stackmat Aktywuje Inspekcję",
    stackmatResetNote: "Uwaga: Działa tylko gdy timer nie jest na 0.000",
    inputTimer: "Tryb Timera Wprowadzania (Wprowadzaj czasy ręcznie)",
    timerMode: "Tryb Timera:",
    timerModeTimer: "Timer",
    timerModeTyping: "Pisanie",
    timerModeStackmat: "Stackmat",
    timerModeBluetooth: "Bluetooth (Wkrótce)",
    microphoneInput: "Wejście Mikrofonu",
    microphoneAuto: "Automatyczne wykrywanie",
    microphoneNote: "Wybierz swój rozdzielacz Y lub zewnętrzny mikrofon",
    decimalPlaces: "Miejsca Dziesiętne:",
    decimalPlacesNone: "Brak (12)",
    decimalPlaces1: "1 (12.3)",
    decimalPlaces2: "2 (12.34)",
    decimalPlaces3: "3 (12.345)",
  },
  displayOptions: {
    title: "Opcje Wyświetlania",
    showVisualization: "Pokaż Wizualizację Układanki",
    showStats: "Pokaż Statystyki",
    showDebug: "Pokaż Informacje Debugowania",
    darkMode: "Tryb Ciemny",
    showFMCKeyboard: "Pokaż Klawiaturę FMC",
    scrambleFontSize: "Rozmiar Czcionki Scramble",
  },
  app: {
    title: "scTimer",
    description: "Timer speedcubingu z inspekcją WCA i statystykami",
    enterTime: "Wprowadź czas",
    enterSolveTime: "Wprowadź czas ułożenia ręcznie",
    generateScrambles: "Generuj Scramble",
    outOf: "Z:",
    numberOfCubes: "Liczba kostek (minimum 2):",
    numberOfCubesSolved: "Liczba ułożonych kostek:",
  },
  timer: {
    ready: "Gotowy",
    running: "Działa",
    idle: "Bezczynny",
    inspection: "Inspekcja",
    holding: "Przytrzymywanie",
  },
  stats: {
    title: "Statystyki",
    best: "Najlepszy",
    worst: "Najgorszy",
    mean: "Średnia",
    avg5: "ao5",
    avg12: "ao12",
    mean3: "mo3",
    bestMo3: "Najlepsze mo3",
    avg100: "ao100",
    avg1000: "ao1000",
    solves: "Ułożenia",
    attempts: "Próby",
    moreStats: "Więcej Statystyk",
  },
  statsDetails: {
    title: "Szczegóły Statystyk",
    titleFor: "Szczegóły Statystyk dla",
    overview: "Przegląd",
    averages: "Średnie",
    records: "Rekordy",
    timeDistribution: "Rozkład Czasów",
    progressChart: "Wykres Postępu",
    sessionAnalysis: "Analiza Sesji",
    predictions: "Przewidywania",
    standardDeviation: "Odchylenie Standardowe",
    bestSingle: "Najlepszy Pojedynczy",
    bestAo5: "Najlepsze ao5",
    bestAo12: "Najlepsze ao12",
    bestAo100: "Najlepsze ao100",
    bestAo1000: "Najlepsze ao1000",
    totalTime: "Całkowity Czas",
    averageTime: "Średni Czas",
    solvesPerHour: "Ułożeń/Godzina",
    consistency: "Konsystencja",
    nextAo5: "Następny Cel ao5",
    nextAo12: "Następny Cel ao12",
    improvementRate: "Tempo Poprawy",
    targetTime: "Czas Docelowy",
    currentSession: "Bieżąca Sesja",
    allSessions: "Wszystkie Sesje",
    importTimes: "Importuj Czasy",
    exportJSON: "Eksportuj JSON",
    exportCSV: "Eksportuj CSV",
  },
  solveDetails: {
    title: "Szczegóły Ułożenia",
    time: "Czas",
    date: "Data",
    scramble: "Scramble",
    editedScramble: "Edytowany Scramble",
    copyScramble: "Kopiuj scramble",
    penalty: "Kara",
    none: "Brak",
    comment: "Komentarz",
    addComment: "Dodaj komentarz...",
    save: "Zapisz",
    share: "Udostępnij",
    plusTwo: "+2",
    dnf: "DNF",
  },
  gestures: {
    scrambleCopied: "Scramble skopiowany",
    noSolvesToDelete: "Brak ułożeń do usunięcia",
    solveDeleted: "Ułożenie usunięte",
    cannotAddPenaltyMBLD: "Nie można dodać kary do ułożenia MBLD",
    dnfRemoved: "DNF usunięte",
    dnfAdded: "DNF dodane",
    plus2Added: "Kara +2 dodana",
    penaltyRemoved: "Kara usunięta",
    newScrambleGenerated: "Nowy scramble wygenerowany",
    timesPanelOpened: "Panel czasów otwarty",
  },
  times: {
    title: "Czasy Ułożeń",
    clear: "Wyczyść Czasy",
    close: "Zamknij",
    delete: "Usuń czas",
    confirmClear:
      "Czy na pewno chcesz wyczyścić wszystkie czasy dla tego wydarzenia?",
    confirmDelete: "Czy na pewno chcesz usunąć ten czas?",
  },
  buttons: {
    viewTimes: "Zobacz Czasy",
    ok: "OK",
    cancel: "Anuluj",
  },
  events: {
    333: "3×3×3",
    222: "2×2×2",
    444: "4×4×4",
    555: "5×5×5",
    666: "6×6×6",
    777: "7×7×7",
    "333bf": "3×3×3 Na Ślepo",
    "333fm": "3×3×3 Najmniej Ruchów",
    "333oh": "3×3×3 Jedną Ręką",
    clock: "Clock",
    minx: "Megaminx",
    pyram: "Pyraminx",
    skewb: "Skewb",
    sq1: "Square-1",
    "444bf": "4×4×4 Na Ślepo",
    "555bf": "5×5×5 Na Ślepo",
    "333mbf": "3×3×3 Multi-Na Ślepo",
  },
  mbld: {
    cubeCount: "Kostki",
    solvedCount: "Ułożone Kostki",
    totalCount: "Wszystkie Kostki",
    totalCubes: "Wszystkie Kostki",
    cubesSolved: "Ułożone Kostki",
    bestPoints: "Najlepsze Punkty",
    successRate: "Wskaźnik Sukcesu",
    points: "punkty",
    save: "Zapisz Wynik",
    visualizations: "Wizualizacje Multi-Na Ślepo",
    scrambles: "Scramble Multi-Na Ślepo",
    enterValidNumber: "Proszę wprowadzić prawidłową liczbę ułożonych kostek.",
    noScrambles:
      "Brak dostępnych scramble MBLD. Proszę najpierw wybrać wydarzenie 3×3×3 Multi-Na Ślepo.",
    visualizationNotFound:
      "Modal wizualizacji nie znaleziony. Proszę odświeżyć stronę i spróbować ponownie.",
    containerNotFound:
      "Kontener wizualizacji nie znaleziony. Proszę odświeżyć stronę i spróbować ponownie.",
    clickToView:
      "Kliknij aby zobaczyć wszystkie wizualizacje i scramble kostek",
    bestScore: "Najlepszy Wynik",
    worstScore: "Najgorszy Wynik",
    meanScore: "Średni Wynik",
    averageScore: "Średni Wynik",
    attempts: "próby",
    totalAttempts: "Wszystkie Próby",
    clickToViewScrambles: "Kliknij aby zobaczyć wszystkie scramble",
    clickToViewScramblesCount: "Kliknij aby zobaczyć wszystkie {0} scramble",
    setup: "Konfiguracja Multi-Na Ślepo",
    results: "Wyniki Multi-Na Ślepo",
    generateScrambles: "Generuj Scramble",
    saveResult: "Zapisz Wynik",
    cubeNumber: "Kostka",
    numberOfCubesMinimum: "Liczba kostek (minimum 2):",
    numberOfCubesSolved: "Liczba ułożonych kostek:",
    saveFirst: "Proszę najpierw zapisać swój wynik.",
    visualizationsTitle: "Wizualizacje Multi-Na Ślepo ({0} kostek)",
    timeLimit: "Limit czasu: {0} minut",
    timeLimitExceeded: "Limit czasu przekroczony. Wynik będzie DNF.",
    negativePoints: "Ujemne punkty. Wynik będzie DNF.",
  },
  modals: {
    error: "Błąd",
    warning: "Ostrzeżenie",
    info: "Informacja",
    confirm: "Potwierdź",
    prompt: "Wymagane Wprowadzenie",
  },
  stackmat: {
    error: "Błąd Stackmat",
    noMicrophone:
      "Uruchomienie timera Stackmat nie powiodło się: Nie znaleziono mikrofonu. Proszę podłączyć mikrofon i spróbować ponownie.",
    connected: "Połączony",
    disconnected: "Rozłączony",
    settingUp: "Konfigurowanie...",
  },
  sessions: {
    newSessionTitle: "Nowa Sesja",
    editSessionTitle: "Edytuj Sesję",
    sessionName: "Nazwa Sesji:",
    sessionNamePlaceholder: "Moja Sesja",
    puzzleType: "Typ Układanki:",
    create: "Utwórz",
    save: "Zapisz",
  },
  scramble: {
    loading: "Ładowanie scramble...",
  },
  debug: {
    timerState: "Stan Timera: ",
    spaceHeldFor: "Spacja Przytrzymana Przez: ",
    currentEvent: "Bieżące Wydarzenie: ",
    scrambleSource: "Źródło Scramble: ",
  },
  fmc: {
    title: "Wyzwanie Najmniej Ruchów",
    info: "Ułóż kostkę używając jak najmniej ruchów. Masz 60 minut na znalezienie rozwiązania.",
    timeRemaining: "Pozostały Czas:",
    scramble: "Scramble:",
    solution: "Rozwiązanie:",
    moveCount: "Ruchy:",
    moves: "ruchów",
    submit: "Wyślij",
    resultTitle: "Wynik FMC",
    resultTime: "Czas:",
    resultSolution: "Rozwiązanie:",
    resultOk: "OK",
    solutionPlaceholder:
      "Wprowadź swoje rozwiązanie tutaj używając standardowej notacji WCA...",
    notationHelp: "Pomoc Notacji:",
    notationHelpContent:
      "Obroty ścian: U, D, L, R, F, B (z przyrostkami ' lub 2)<br>Szerokie ruchy: Uw, Dw, itp.<br>Ruchy warstw: M, E, S<br>Rotacje: x, y, z (nie liczone w całkowitej liczbie ruchów)",
    submitSolution: "Wyślij Rozwiązanie",
    validSolution: "Prawidłowe rozwiązanie",
    invalidNotation: "Wykryto nieprawidłową notację",
    bestMoves: "Najlepsze Ruchy",
    worstMoves: "Najgorsze Ruchy",
    meanMoves: "Średnie Ruchy",
    bestMo3: "Najlepsze mo3",
    averageMoves: "Średnie Ruchy",
    attempts: "próby",
    totalAttempts: "Wszystkie Próby",
    tooManyMoves: "Rozwiązanie przekracza limit 80 ruchów",
    timeExceeded:
      "Limit czasu przekroczony. Twoje rozwiązanie zostanie oznaczone jako DNF jeśli nie zostanie wysłane.",
    confirmClose:
      "Czy na pewno chcesz zamknąć? Twoja próba zostanie oznaczona jako DNF.",
    dnfReasonTimeout: "Limit czasu przekroczony",
    dnfReasonInvalid: "Nieprawidłowa notacja",
    dnfReasonTooManyMoves: "Rozwiązanie przekracza 80 ruchów",
    dnfReasonAbandoned: "Próba porzucona",
    confirmSubmit: "Czy na pewno chcesz wysłać swoje rozwiązanie?",
    pressToStart: "Naciśnij spację aby rozpocząć próbę FMC",
    solutionAccepted: "Rozwiązanie zaakceptowane",
    clickToViewTwizzle:
      "Kliknij link poniżej aby zobaczyć rozwiązanie w Twizzle",
    viewOnTwizzle: "Zobacz w Twizzle",
    moveCountLabel: "Liczba ruchów:",
    movesHTM: "ruchów (HTM)",
    timeUsedLabel: "Użyty czas:",
    loadingFMC: "ładowanie FMC",
    generatingScramble: "generowanie scramble i przygotowywanie interfejsu",
  },
  tutorial: {
    welcomeTitle: "Witaj w scTimer!",
    welcomeSubtitle: "Twój profesjonalny timer speedcubingu",
    selectLanguage: "Wybierz Język:",
    feature1: "Timer Standardu WCA",
    feature2: "Zaawansowane Statystyki",
    feature3: "Wszystkie Wydarzenia WCA",
    feature4: "Generator Scramble",
    welcomeDescription:
      "Czy chciałbyś szybką wycieczkę aby nauczyć się jak efektywnie używać scTimer? Tutorial poprowadzi Cię przez główne funkcje w zaledwie kilku krokach.",
    skipTutorial: "Pomiń Tutorial",
    startTour: "Rozpocznij Tour",
    step1: {
      title: "Wyświetlanie Scramble",
      text: "To pokazuje sekwencję scramble dla Twojej bieżącej układanki. Każdy scramble jest generowany losowo zgodnie ze standardami WCA.",
    },
    step2: {
      title: "Sterowanie Timerem",
      text: "Naciśnij i przytrzymaj SPACJĘ aby rozpocząć pomiar czasu, puść aby rozpocząć układanie. Na urządzeniach mobilnych dotknij i przytrzymaj obszar timera. Timer przestrzega standardów inspekcji WCA.",
    },
    step3: {
      title: "Selektor Wydarzenia",
      text: "Wybierz spośród wszystkich wydarzeń WCA włączając 3x3x3, 2x2x2, 4x4x4 i wiele innych typów układanek. Kliknij lub dotknij aby otworzyć menu rozwijane.",
    },
    step4: {
      title: "Śledzenie Statystyk",
      text: "Śledź swój postęp ze szczegółowymi statystykami włączając najlepszy czas, średnie z 5, 12 i 100 ułożeń. Kliknij na dowolną statystykę aby zobaczyć więcej szczegółów.",
    },
    step5: {
      title: "Generowanie Nowego Scramble",
      text: "Generuj nowy scramble gdy jesteś gotowy na następne ułożenie. Skrót klawiszowy: Naciśnij N lub kliknij ikonę tasowania.",
    },
    step6: {
      title: "Ustawienia i Dostosowywanie",
      text: "Dostosuj swoje doświadczenie z timerem za pomocą czasu inspekcji, opcji dźwięku, trybów timera i preferencji wyświetlania. Skrót klawiszowy: Naciśnij S.",
    },
    step7: {
      title: "Skróty Klawiszowe",
      text: "Opanuj te skróty: SPACJA (start/stop timer), N (nowy scramble), S (ustawienia), ESC (zamknij modale), Strzałki (nawigacja). Na urządzeniach mobilnych używaj gestów przesuwania!",
    },
    step8: {
      title: "Gesty Mobilne",
      text: "Na urządzeniach mobilnych: Przesuń w lewo aby otworzyć panel czasów, przesuń w prawo aby zamknąć, dotknij i przytrzymaj timer aby rozpocząć, podwójnie dotknij scramble aby skopiować. Uszczypnij aby powiększyć wizualizacje.",
    },
    step9: {
      title: "Profesjonalne Wskazówki i Funkcje",
      text: "Włącz czas inspekcji w ustawieniach dla praktyki WCA. Używaj różnych sesji aby śledzić różne wydarzenia. Eksportuj swoje czasy do analizy. Timer działa offline jako PWA!",
    },
    previous: "Poprzedni",
    next: "Następny",
    finish: "Zakończ",
    close: "Zamknij",
    stepCounter: "z",
    restartTutorial: "Uruchom Tutorial Ponownie",
  },
};

// German language file
export default {
  dir: "ltr",
  settings: {
    title: "Einstellungen",
    save: "<PERSON><PERSON><PERSON><PERSON>",
    close: "Schließen",
    language: "Sprache",
  },
  info: {
    title: "Informationen",
    pwaInstall: {
      title: "Als App installieren",
      description:
        "Installiere scTimer als Progressive Web App für die beste Erfahrung. Funktioniert offline und fühlt sich wie eine native App an.",
      install: "App installieren",
      iosTitle: "iOS/iPad Installation:",
      iosStep1: "1. Tippe auf den Teilen-Button",
      iosStep2: '2. Scrolle nach unten und tippe auf "Zum Home-Bildschirm"',
      iosStep3: '3. Tippe auf "Hinzufügen" zum Installieren',
      note: "Verfügbar in Chrome, Safari und anderen modernen Browsern",
    },
    shortcuts: {
      title: "Tastaturkürzel",
      timer: "Timer-Steuerung",
      spacebar: "Timer starten/stoppen",
      escape: "Inspektion abbrechen und Modals schließen",
      navigation: "Navigation und Aktionen",
      generate: "Neuen Scramble generieren",
      list: "Zeiten-Liste umschalten",
      settings: "Einstellungen öffnen",
      edit: "Aktuellen Scramble bearbeiten",
      copy: "Scramble in Zwischenablage kopieren",
      stats: "Detaillierte Statistiken öffnen",
      display: "Anzeige umschalten",
      visualization: "Puzzle-Visualisierung umschalten",
      statistics: "Statistik-Anzeige umschalten",
      darkMode: "Dunklen Modus umschalten",
      inspection: "WCA-Inspektion umschalten",
      penalties: "Strafen-Verwaltung",
      removePenalty: "Strafe vom letzten Solve entfernen",
      addPlus2: "+2 Strafe zum letzten Solve hinzufügen",
      addDNF: "DNF Strafe zum letzten Solve hinzufügen",
      session: "Session-Verwaltung",
      emptySession: "Aktuelle Session leeren",
      exportSession: "Aktuelle Session exportieren",
      eventSwitching: "Event-Wechsel",
      alt2to7: "Zu 2×2×2 bis 7×7×7 Würfeln wechseln",
      altP: "Zu Pyraminx wechseln",
      altM: "Zu Megaminx wechseln",
      altC: "Zu Clock wechseln",
      altS: "Zu Skewb wechseln",
      alt1: "Zu Square-1 wechseln",
      altF: "Zu 3×3×3 Wenigste Züge wechseln",
      altO: "Zu 3×3×3 Einhändig wechseln",
      blindfolded: "Blindfolded Events",
      altCtrl3: "Zu 3×3×3 Blindfolded wechseln",
      altCtrl4: "Zu 4×4×4 Blindfolded wechseln",
      altCtrl5: "Zu 5×5×5 Blindfolded wechseln",
      altCtrl6: "Zu 3×3×3 Multi-Blind wechseln",
      sessionMgmt: "Session-Verwaltung",
      altN: "Neue Session erstellen",
    },
    gestures: {
      title: "Mobile Gesten",
      swipeDown: "Nach unten wischen",
      swipeDownDesc: "Letzten Solve löschen",
      swipeUp: "Nach oben wischen",
      swipeUpDesc: "Strafen durchschalten (keine/+2/DNF)",
      swipeLeft: "Nach links wischen",
      swipeLeftDesc: "LTR: Neuer Scramble | RTL: Zeiten-Liste",
      swipeRight: "Nach rechts wischen",
      swipeRightDesc: "LTR: Zeiten-Liste | RTL: Neuer Scramble",
      doubleClick: "Doppelklick",
      doubleClickDesc: "Aktuellen Scramble kopieren (PC/Mobile)",
      longPress: "Langer Druck/Klick und Halten",
      longPressDesc: "Aktuellen Scramble bearbeiten (PC/Mobile)",
    },
    features: {
      title: "Hauptfunktionen",
      timer: "Professioneller Timer",
      timerDesc: "WCA-konformes Timing mit Inspektionsmodus",
      puzzles: "Alle WCA Events",
      puzzlesDesc:
        "Vollständige Unterstützung für alle offiziellen WCA Puzzle-Events",
      statistics: "Erweiterte Statistiken",
      statisticsDesc: "Detaillierte Analysen mit ao5, ao12, ao100",
      scrambles: "Offizielle Scrambles",
      scramblesDesc: "WCA-Standard Scramble-Generierung mit 2D-Visualisierung",
      multilingual: "Mehrsprachige Unterstützung",
      multilingualDesc: "15+ Sprachen mit RTL-Unterstützung",
      sync: "Google Drive Sync",
      syncDesc: "Geräteübergreifende Synchronisation mit intelligentem Merging",
    },
    sync: {
      title: "Google Drive Synchronisation",
      description:
        "Synchronisiere deine Solve-Zeiten auf allen Geräten mit Google Drive. Deine Daten werden sicher in deinem persönlichen Google Drive-Konto gespeichert.",
      secure: "Sicher und Privat",
      automatic: "Automatische Synchronisation",
      offline: "Offline-Unterstützung",
      smartMerge: "Intelligentes Merging",
      note: "Aktiviere Google Drive Sync in den Einstellungen, um deine Zeiten auf allen Geräten synchron zu halten.",
      status: "Status:",
      notConnected: "Nicht verbunden",
      connected: "Verbunden",
      connect: "Verbinden",
      disconnect: "Trennen",
      upload: "Zu Drive hochladen",
      download: "Von Drive herunterladen",
      autoSync: "Automatische Synchronisation",
      autoSyncNote:
        "Synchronisiere deine Zeiten automatisch, wenn du mit dem Internet verbunden bist",
      uploading: "Hochladen...",
      downloading: "Herunterladen...",
      syncing: "Synchronisieren...",
      uploadSuccess: "Upload erfolgreich",
      downloadSuccess: "Download erfolgreich",
      uploadFailed: "Upload fehlgeschlagen",
      downloadFailed: "Download fehlgeschlagen",
      uploadConfirm:
        "Lokale Zeiten zu Google Drive hochladen und zusammenführen?",
      downloadConfirm:
        "Daten von Google Drive herunterladen und mit lokalen Zeiten zusammenführen?",
      downloadMergeConfirm:
        "Dies wird Google Drive-Daten mit deinen lokalen Zeiten zusammenführen. Fortfahren?",
      reloadConfirm: "Seite neu laden, um Änderungen zu sehen?",
      autoSyncEnabled: "Automatische Synchronisation aktiviert",
      signInFailed: "Anmeldung fehlgeschlagen",
      noSyncFile: "Keine Synchronisationsdatei gefunden",
      noDataFound: "Keine Daten gefunden",
      uploadCancelled: "Upload abgebrochen",
      downloadCancelled: "Download abgebrochen",
      syncSuccessful: "Synchronisation erfolgreich",
      syncFailed: "Synchronisation fehlgeschlagen",
      error: "Fehler",
    },
  },
  timerOptions: {
    title: "Timer-Optionen",
    warningSounds: "Warntöne aktivieren",
    useInspection: "WCA-Inspektion verwenden (15s)",
    inspectionSound: "Inspektionston:",
    inspectionSoundNone: "Keiner",
    inspectionSoundVoice: "Stimme",
    inspectionSoundBeep: "Piep",
    stackmatResetInspection: "Stackmat Reset löst Inspektion aus",
    stackmatResetNote:
      "Hinweis: Funktioniert nur wenn Timer nicht auf 0.000 steht",
    inputTimer: "Eingabe-Timer-Modus (Zeiten manuell eingeben)",
    timerMode: "Timer-Modus:",
    timerModeTimer: "Timer",
    timerModeTyping: "Eingabe",
    timerModeStackmat: "Stackmat",
    timerModeBluetooth: "Bluetooth (Demnächst)",
    microphoneInput: "Mikrofon-Eingabe",
    microphoneAuto: "Automatische Erkennung",
    microphoneNote: "Wähle deinen Y-Splitter oder externes Mikrofon",
    decimalPlaces: "Dezimalstellen:",
    decimalPlacesNone: "Keine (12)",
    decimalPlaces1: "1 (12.3)",
    decimalPlaces2: "2 (12.34)",
    decimalPlaces3: "3 (12.345)",
  },
  displayOptions: {
    title: "Anzeige-Optionen",
    showVisualization: "Puzzle-Visualisierung anzeigen",
    showStats: "Statistiken anzeigen",
    showDebug: "Debug-Informationen anzeigen",
    darkMode: "Dunkler Modus",
    showFMCKeyboard: "FMC-Tastatur anzeigen",
    scrambleFontSize: "Scramble-Schriftgröße",
  },
  app: {
    title: "scTimer",
    description: "Ein Speedcubing-Timer mit WCA-Inspektion und Statistiken",
    enterTime: "Zeit eingeben",
    enterSolveTime: "Solve-Zeit manuell eingeben",
    generateScrambles: "Scrambles generieren",
    outOf: "Von:",
    numberOfCubes: "Anzahl Würfel (mindestens 2):",
    numberOfCubesSolved: "Anzahl gelöster Würfel:",
  },
  timer: {
    ready: "Bereit",
    running: "Läuft",
    idle: "Inaktiv",
    inspection: "Inspektion",
    holding: "Gehalten",
  },
  stats: {
    title: "Statistiken",
    best: "Beste",
    worst: "Schlechteste",
    mean: "Mittelwert",
    avg5: "ao5",
    avg12: "ao12",
    mean3: "mo3",
    bestMo3: "Beste mo3",
    avg100: "ao100",
    avg1000: "ao1000",
    solves: "Solves",
    attempts: "Versuche",
    moreStats: "Mehr Stats",
  },
  statsDetails: {
    title: "Statistik-Details",
    titleFor: "Statistik-Details für",
    overview: "Übersicht",
    averages: "Durchschnitte",
    records: "Rekorde",
    timeDistribution: "Zeit-Verteilung",
    progressChart: "Fortschritt über Zeit",
    sessionAnalysis: "Session-Analyse",
    predictions: "Vorhersagen",
    standardDeviation: "Standardabweichung",
    bestSingle: "Beste Einzelzeit",
    bestAo5: "Beste ao5",
    bestAo12: "Beste ao12",
    bestAo100: "Beste ao100",
    bestAo1000: "Beste ao1000",
    totalTime: "Gesamtzeit",
    averageTime: "Durchschnittszeit",
    solvesPerHour: "Solves/Stunde",
    consistency: "Konsistenz",
    nextAo5: "Nächstes ao5 Ziel",
    nextAo12: "Nächstes ao12 Ziel",
    improvementRate: "Verbesserungsrate",
    targetTime: "Zielzeit",
    currentSession: "Aktuelle Session",
    allSessions: "Alle Sessions",
    importTimes: "Zeiten importieren",
    exportJSON: "JSON exportieren",
    exportCSV: "CSV exportieren",
  },
  solveDetails: {
    title: "Solve-Details",
    time: "Zeit",
    date: "Datum",
    scramble: "Scramble",
    editedScramble: "Bearbeiteter Scramble",
    copyScramble: "Scramble kopieren",
    penalty: "Strafe",
    none: "Keine",
    comment: "Kommentar",
    addComment: "Kommentar hinzufügen...",
    save: "Speichern",
    share: "Teilen",
    plusTwo: "+2",
    dnf: "DNF",
  },
  gestures: {
    scrambleCopied: "Scramble kopiert",
    noSolvesToDelete: "Keine Solves zum Löschen",
    solveDeleted: "Solve gelöscht",
    cannotAddPenaltyMBLD: "Kann keine Strafe zu MBLD Solve hinzufügen",
    dnfRemoved: "DNF entfernt",
    dnfAdded: "DNF hinzugefügt",
    plus2Added: "+2 Strafe hinzugefügt",
    penaltyRemoved: "Strafe entfernt",
    newScrambleGenerated: "Neuer Scramble generiert",
    timesPanelOpened: "Zeiten-Panel geöffnet",
  },
  times: {
    title: "Solve-Zeiten",
    clear: "Zeiten löschen",
    close: "Schließen",
    delete: "Zeit löschen",
    confirmClear:
      "Bist du sicher, dass du alle Zeiten für dieses Event löschen möchtest?",
    confirmDelete: "Bist du sicher, dass du diese Zeit löschen möchtest?",
  },
  buttons: {
    viewTimes: "Zeiten anzeigen",
    ok: "OK",
    cancel: "Abbrechen",
  },
  events: {
    333: "3×3×3",
    222: "2×2×2",
    444: "4×4×4",
    555: "5×5×5",
    666: "6×6×6",
    777: "7×7×7",
    "333bf": "3×3×3 Blindfolded",
    "333fm": "3×3×3 Wenigste Züge",
    "333oh": "3×3×3 Einhändig",
    clock: "Clock",
    minx: "Megaminx",
    pyram: "Pyraminx",
    skewb: "Skewb",
    sq1: "Square-1",
    "444bf": "4×4×4 Blindfolded",
    "555bf": "5×5×5 Blindfolded",
    "333mbf": "3×3×3 Multi-Blind",
  },
  mbld: {
    cubeCount: "Würfel",
    solvedCount: "Gelöste Würfel",
    totalCount: "Würfel Gesamt",
    totalCubes: "Würfel Gesamt",
    cubesSolved: "Gelöste Würfel",
    bestPoints: "Beste Punkte",
    successRate: "Erfolgsrate",
    points: "punkte",
    save: "Ergebnis speichern",
    visualizations: "Multi-Blind Visualisierungen",
    scrambles: "Multi-Blind Scrambles",
    enterValidNumber: "Bitte gib eine gültige Anzahl gelöster Würfel ein.",
    noScrambles:
      "Keine MBLD Scrambles verfügbar. Bitte wähle zuerst das 3×3×3 Multi-Blind Event.",
    visualizationNotFound:
      "Visualisierungs-Modal nicht gefunden. Bitte lade die Seite neu und versuche es erneut.",
    containerNotFound:
      "Visualisierungs-Container nicht gefunden. Bitte lade die Seite neu und versuche es erneut.",
    clickToView:
      "Klicke um alle Würfel-Visualisierungen und Scrambles zu sehen",
    bestScore: "Beste Punktzahl",
    worstScore: "Schlechteste Punktzahl",
    meanScore: "Durchschnittliche Punktzahl",
    averageScore: "Durchschnittliche Punktzahl",
    attempts: "versuche",
    totalAttempts: "Versuche Gesamt",
    clickToViewScrambles: "Klicke um alle Scrambles zu sehen",
    clickToViewScramblesCount: "Klicke um alle {0} Scrambles zu sehen",
    setup: "Multi-Blind Setup",
    results: "Multi-Blind Ergebnisse",
    generateScrambles: "Scrambles generieren",
    saveResult: "Ergebnis speichern",
    cubeNumber: "Würfel",
    numberOfCubesMinimum: "Anzahl Würfel (mindestens 2):",
    numberOfCubesSolved: "Anzahl gelöster Würfel:",
    saveFirst: "Bitte speichere zuerst dein Ergebnis.",
    visualizationsTitle: "Multi-Blind Visualisierungen ({0} Würfel)",
    timeLimit: "Zeitlimit: {0} Minuten",
    timeLimitExceeded: "Zeitlimit überschritten. Ergebnis wird DNF.",
    negativePoints: "Negative Punkte. Ergebnis wird DNF.",
  },
  modals: {
    error: "Fehler",
    warning: "Warnung",
    info: "Information",
    confirm: "Bestätigen",
    prompt: "Eingabe erforderlich",
  },
  stackmat: {
    error: "Stackmat Fehler",
    noMicrophone:
      "Stackmat Timer konnte nicht gestartet werden: Kein Mikrofon gefunden. Bitte verbinde ein Mikrofon und versuche es erneut.",
    connected: "Verbunden",
    disconnected: "Getrennt",
    settingUp: "Einrichten...",
  },
  sessions: {
    newSessionTitle: "Neue Session",
    editSessionTitle: "Session bearbeiten",
    sessionName: "Session-Name:",
    sessionNamePlaceholder: "Meine Session",
    puzzleType: "Puzzle-Typ:",
    create: "Erstellen",
    save: "Speichern",
  },
  scramble: {
    loading: "Lade Scramble...",
  },
  debug: {
    timerState: "Timer-Status: ",
    spaceHeldFor: "Leertaste gehalten für: ",
    currentEvent: "Aktuelles Event: ",
    scrambleSource: "Scramble-Quelle: ",
  },
  fmc: {
    title: "Wenigste Züge Herausforderung",
    info: "Löse den Würfel mit so wenigen Zügen wie möglich. Du hast 60 Minuten um eine Lösung zu finden.",
    timeRemaining: "Zeit:",
    scramble: "Scramble:",
    solution: "Lösung:",
    moveCount: "Züge:",
    moves: "züge",
    submit: "Abschicken",
    resultTitle: "FMC Ergebnis",
    resultTime: "Zeit:",
    resultSolution: "Lösung:",
    resultOk: "OK",
    solutionPlaceholder:
      "Gib deine Lösung hier mit Standard WCA-Notation ein...",
    notationHelp: "Notations-Hilfe:",
    notationHelpContent:
      "Flächendrehungen: U, D, L, R, F, B (mit ' oder 2 Suffixen)<br>Breite Züge: Uw, Dw, etc.<br>Scheiben-Züge: M, E, S<br>Rotationen: x, y, z (nicht in Zug-Gesamtzahl gezählt)",
    submitSolution: "Lösung abschicken",
    validSolution: "Gültige Lösung",
    invalidNotation: "Ungültige Notation erkannt",
    bestMoves: "Beste Züge",
    worstMoves: "Schlechteste Züge",
    meanMoves: "Durchschnittliche Züge",
    bestMo3: "Beste mo3",
    averageMoves: "Durchschnittliche Züge",
    attempts: "versuche",
    totalAttempts: "Versuche Gesamt",
    tooManyMoves: "Lösung überschreitet 80-Züge-Limit",
    timeExceeded:
      "Zeitlimit überschritten. Deine Lösung wird als DNF markiert wenn nicht abgeschickt.",
    confirmClose:
      "Bist du sicher, dass du schließen möchtest? Dein Versuch wird als DNF markiert.",
    dnfReasonTimeout: "Zeitlimit überschritten",
    dnfReasonInvalid: "Ungültige Notation",
    dnfReasonTooManyMoves: "Lösung überschreitet 80 Züge",
    dnfReasonAbandoned: "Versuch abgebrochen",
    confirmSubmit: "Bist du sicher, dass du deine Lösung abschicken möchtest?",
    pressToStart: "Drücke Leertaste um FMC-Versuch zu starten",
    solutionAccepted: "Lösung akzeptiert",
    clickToViewTwizzle:
      "Klicke auf den Link unten um die Lösung in Twizzle zu sehen",
    viewOnTwizzle: "In Twizzle anzeigen",
    moveCountLabel: "Anzahl Züge:",
    movesHTM: "züge (HTM)",
    timeUsedLabel: "Verwendete Zeit:",
    loadingFMC: "lade FMC",
    generatingScramble: "generiere Scramble und bereite Interface vor",
  },
  tutorial: {
    welcomeTitle: "Willkommen bei scTimer!",
    welcomeSubtitle: "Dein professioneller Speedcubing-Timer",
    selectLanguage: "Sprache auswählen:",
    feature1: "WCA Standard Timer",
    feature2: "Erweiterte Statistiken",
    feature3: "Alle WCA Events",
    feature4: "Scramble-Generator",
    welcomeDescription:
      "Möchtest du eine kurze Tour um zu lernen, wie man scTimer effektiv nutzt? Das Tutorial führt dich durch die Hauptfunktionen in nur wenigen Schritten.",
    skipTutorial: "Tutorial überspringen",
    startTour: "Tour starten",
    step1: {
      title: "Scramble-Anzeige",
      text: "Dies zeigt die Scramble-Sequenz für dein aktuelles Puzzle. Jeder Scramble wird zufällig nach WCA-Standards generiert.",
    },
    step2: {
      title: "Timer-Steuerung",
      text: "Drücke und halte die LEERTASTE um das Timing zu starten, loslassen um mit dem Lösen zu beginnen. Auf Mobilgeräten tippe und halte den Timer-Bereich. Der Timer folgt WCA-Inspektionsstandards.",
    },
    step3: {
      title: "Event-Auswahl",
      text: "Wähle aus allen WCA-Events einschließlich 3x3x3, 2x2x2, 4x4x4 und vielen weiteren Puzzle-Typen. Klicke oder tippe um das Dropdown-Menü zu öffnen.",
    },
    step4: {
      title: "Statistik-Verfolgung",
      text: "Verfolge deinen Fortschritt mit detaillierten Statistiken einschließlich bester Zeit, Durchschnitten von 5, 12 und 100 Solves. Klicke auf jede Statistik um mehr Details zu sehen.",
    },
    step5: {
      title: "Neuen Scramble generieren",
      text: "Generiere einen neuen Scramble wenn du bereit für deinen nächsten Solve bist. Tastaturkürzel: Drücke N oder klicke auf das Misch-Symbol.",
    },
    step6: {
      title: "Einstellungen und Anpassung",
      text: "Passe deine Timer-Erfahrung mit Inspektionszeit, Soundoptionen, Timer-Modi und Anzeige-Einstellungen an. Tastaturkürzel: Drücke S.",
    },
    step7: {
      title: "Tastaturkürzel",
      text: "Meistere diese Kürzel: LEERTASTE (Timer starten/stoppen), N (neuer Scramble), S (Einstellungen), ESC (Modals schließen), Pfeiltasten (navigieren). Auf Mobilgeräten verwende Wischgesten!",
    },
    step8: {
      title: "Mobile Gesten",
      text: "Auf Mobilgeräten: Wische nach links um das Zeiten-Panel zu öffnen, wische nach rechts um es zu schließen, tippe und halte den Timer zum Starten, doppeltippe den Scramble zum Kopieren. Kneifen zum Zoomen bei Visualisierungen.",
    },
    step9: {
      title: "Profi-Tipps und Funktionen",
      text: "Aktiviere Inspektionszeit in den Einstellungen für WCA-Übung. Verwende verschiedene Sessions um verschiedene Events zu verfolgen. Exportiere deine Zeiten zur Analyse. Der Timer funktioniert offline als PWA!",
    },
    previous: "Zurück",
    next: "Weiter",
    finish: "Beenden",
    close: "Schließen",
    stepCounter: "von",
    restartTutorial: "Tutorial neu starten",
  },
};

# Fonts for scTimer

This folder is for storing font files used by the scTimer application.

## Recommended Fonts

The application is configured to use the following fonts:

1. **Roboto** - For general text

   - Roboto-Regular.woff2
   - Roboto-Bold.woff2

2. **Digital-7** - For the timer display

   - digital-7.woff2

3. **Noto Sans** - For multilingual support

   - NotoSans-Regular.woff2

4. **Rabar** - For Kurdish (RTL) text
   - Rabar_22.ttf (Normal)
   - Rabar_21.ttf (Bold)

## How to Add Fonts

1. Download the font files in WOFF2 and WOFF formats
2. Place them in this folder
3. The CSS is already configured to use these fonts

## Font Sources

- Roboto: https://fonts.google.com/specimen/Roboto
- Digital-7: https://www.dafont.com/digital-7.font
- Noto Sans: https://fonts.google.com/noto/specimen/Noto+Sans
- Rabar: Kurdish font for RTL text

## License Information

Please ensure you have the proper licenses for any fonts you use in your application.

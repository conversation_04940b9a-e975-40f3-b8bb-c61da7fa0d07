/**
 * Audio Manager for sc<PERSON><PERSON>r
 * Handles audio playback with special considerations for iOS devices
 */

// Audio context for Web Audio API
let audioContext = null;
let audioInitialized = false;
let audioBuffers = {};
let isIOS = false;

// Check if running on iOS
function detectIOS() {
  return (
    ["iPad", "iPhone", "iPod"].includes(navigator.platform) ||
    (navigator.userAgent.includes("Mac") && "ontouchend" in document)
  );
}

// Initialize audio system
function initAudio() {
  if (audioInitialized) return;

  isIOS = detectIOS();

  try {
    // Create audio context
    const AudioContext = window.AudioContext || window.webkitAudioContext;
    audioContext = new AudioContext();

    // Load sound files
    loadSound("8sec", "assets/sounds/8sec.wav");
    loadSound("12sec", "assets/sounds/12sec.wav");

    // Load voice sound files for different languages
    loadSound("8sec-en", "assets/sounds/8sec-en.mp3");
    loadSound("12sec-en", "assets/sounds/12sec-en.mp3");
    loadSound("8sec-ar", "assets/sounds/8sec-ar.mp3");
    loadSound("12sec-ar", "assets/sounds/12sec-ar.mp3");
    loadSound("8sec-ckb", "assets/sounds/8sec-ckb.mp3");
    loadSound("12sec-ckb", "assets/sounds/12sec-ckb.mp3");

    audioInitialized = true;
  } catch (error) {
    // Audio initialization failed - continue without audio
  }
}

// Load a sound file into buffer
function loadSound(name, url) {
  fetch(url)
    .then((response) => response.arrayBuffer())
    .then((arrayBuffer) => audioContext.decodeAudioData(arrayBuffer))
    .then((audioBuffer) => {
      audioBuffers[name] = audioBuffer;
    })
    .catch((error) => {
      // Sound loading failed - continue without this sound
    });
}

// Play a sound with iOS workarounds
function playSound(name) {
  if (!audioInitialized || !audioContext) return false;

  // Check if the sound is loaded
  if (!audioBuffers[name]) return false;

  try {
    // Create a new buffer source
    const source = audioContext.createBufferSource();
    source.buffer = audioBuffers[name];
    source.connect(audioContext.destination);

    // Start playback
    if (audioContext.state === "suspended") {
      audioContext.resume().then(() => source.start(0));
    } else {
      source.start(0);
    }

    return true;
  } catch (error) {
    // Sound playback failed
    return false;
  }
}

// Fallback to HTML5 Audio
function playFallbackSound(id) {
  const audioElement = document.getElementById(id);
  if (!audioElement) return false;

  try {
    audioElement.currentTime = 0;
    const playPromise = audioElement.play();

    if (playPromise !== undefined) {
      playPromise.catch(() => {
        // Auto-play was prevented, try to unlock audio on next user interaction
        document.addEventListener("touchstart", unlockAudio, { once: true });
      });
    }

    return true;
  } catch (error) {
    return false;
  }
}

// Unlock audio on iOS
function unlockAudio() {
  if (audioContext && audioContext.state === "suspended") {
    audioContext.resume();
  }

  // Also try to unlock HTML5 audio
  const audioElements = document.querySelectorAll("audio");
  audioElements.forEach((audio) => {
    audio.load();
    audio.play().catch(() => {});
    audio.pause();
  });
}

// Play 8-second warning sound
function play8SecSound() {
  // Try Web Audio API first
  if (playSound("8sec")) return true;

  // Fall back to HTML5 Audio
  return playFallbackSound("beep-sound");
}

// Play 12-second warning sound
function play12SecSound() {
  // Try Web Audio API first
  if (playSound("12sec")) return true;

  // Fall back to HTML5 Audio
  return playFallbackSound("double-beep-sound");
}

// Play voice sound using pre-recorded audio files
function playVoiceSound(type) {
  if (!audioInitialized || !audioContext) {
    // Fall back to beep sounds if audio is not initialized
    if (type === "8sec") {
      return play8SecSound();
    } else if (type === "12sec") {
      return play12SecSound();
    }
    return false;
  }

  // Get current language from localStorage or default to English
  const currentLang = localStorage.getItem("scTimer-language") || "en";

  // Create the sound key based on type and language
  const soundKey = `${type}-${currentLang}`;

  // Try to play the language-specific voice sound
  if (playSound(soundKey)) {
    return true;
  }

  // Fall back to English if the current language sound is not available
  if (currentLang !== "en") {
    const englishSoundKey = `${type}-en`;
    if (playSound(englishSoundKey)) {
      return true;
    }
  }

  // Final fallback to beep sounds
  if (type === "8sec") {
    return play8SecSound();
  } else if (type === "12sec") {
    return play12SecSound();
  }

  return false;
}

// Initialize on page load
document.addEventListener("DOMContentLoaded", function () {
  // Wait for user interaction to initialize audio
  const initOnInteraction = function () {
    initAudio();
    unlockAudio();

    // Remove event listeners once initialized
    document.removeEventListener("touchstart", initOnInteraction);
    document.removeEventListener("mousedown", initOnInteraction);
    document.removeEventListener("keydown", initOnInteraction);
  };

  document.addEventListener("touchstart", initOnInteraction);
  document.addEventListener("mousedown", initOnInteraction);
  document.addEventListener("keydown", initOnInteraction);
});

// Export functions
export {
  initAudio,
  unlockAudio,
  play8SecSound,
  play12SecSound,
  playVoiceSound,
};
